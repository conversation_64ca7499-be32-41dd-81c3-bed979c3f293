package com.live;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 直播系统启动类
 */
@SpringBootApplication
@MapperScan("com.live.mapper")
@EnableAsync
@EnableScheduling
public class LiveSystemApplication {

    public static void main(String[] args) {
        SpringApplication.run(LiveSystemApplication.class, args);
    }
}
