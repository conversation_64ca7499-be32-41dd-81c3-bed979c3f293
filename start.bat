@echo off
chcp 65001 >nul

echo 正在启动直播系统...

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请先安装JDK 8或更高版本
    pause
    exit /b 1
)

REM 检查Maven环境
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Maven环境，请先安装Maven
    pause
    exit /b 1
)

REM 编译项目
echo 正在编译项目...
mvn clean compile -q

if %errorlevel% neq 0 (
    echo 错误: 项目编译失败
    pause
    exit /b 1
)

REM 启动应用
echo 正在启动应用...
mvn spring-boot:run

echo 直播系统启动完成！
echo 访问地址: http://localhost:8080
echo API文档: http://localhost:8080/api
echo 数据库监控: http://localhost:8080/druid
pause
