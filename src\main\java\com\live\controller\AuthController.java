package com.live.controller;

import com.alibaba.fastjson.JSONObject;
import com.live.common.Result;
import com.live.entity.Anchor;
import com.live.entity.User;
import com.live.service.AnchorService;
import com.live.service.UserService;
import com.live.service.WechatService;
import com.live.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final WechatService wechatService;
    private final UserService userService;
    private final AnchorService anchorService;
    private final JwtUtil jwtUtil;

    /**
     * 获取微信授权URL
     */
    @GetMapping("/wechat/auth-url")
    public Result<String> getWechatAuthUrl(@RequestParam(required = false) String state) {
        try {
            if (state == null || state.trim().isEmpty()) {
                state = "default";
            }
            String authUrl = wechatService.getAuthUrl(state);
            return Result.success(authUrl);
        } catch (Exception e) {
            log.error("获取微信授权URL失败", e);
            return Result.error("获取微信授权URL失败");
        }
    }

    /**
     * 微信授权回调
     */
    @GetMapping("/wechat/callback")
    public Result<Map<String, Object>> wechatCallback(
            @RequestParam String code,
            @RequestParam(required = false) String state,
            HttpServletRequest request) {
        try {
            log.info("微信授权回调：code={}, state={}", code, state);

            // 1. 获取access_token
            JSONObject tokenResult = wechatService.getAccessToken(code);
            String accessToken = tokenResult.getString("access_token");
            String openid = tokenResult.getString("openid");

            // 2. 获取用户信息
            JSONObject userInfo = wechatService.getUserInfo(accessToken, openid);
            
            // 3. 转换为User实体并保存或更新
            User user = wechatService.convertToUser(userInfo);
            user = userService.createOrUpdateUser(user);

            // 4. 生成JWT token
            String token = jwtUtil.generateUserToken(user.getId(), user.getOpenid());

            // 5. 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("user", user);

            log.info("用户微信授权登录成功：userId={}, openid={}", user.getId(), user.getOpenid());
            return Result.success("登录成功", result);

        } catch (Exception e) {
            log.error("微信授权回调处理失败", e);
            return Result.error("微信授权失败：" + e.getMessage());
        }
    }

    /**
     * 主播登录
     */
    @PostMapping("/anchor/login")
    public Result<Map<String, Object>> anchorLogin(@RequestBody Map<String, String> loginData) {
        try {
            String username = loginData.get("username");
            String password = loginData.get("password");

            if (username == null || username.trim().isEmpty()) {
                return Result.badRequest("用户名不能为空");
            }
            if (password == null || password.trim().isEmpty()) {
                return Result.badRequest("密码不能为空");
            }

            // 验证登录
            Anchor anchor = anchorService.login(username, password);
            if (anchor == null) {
                return Result.error("用户名或密码错误");
            }

            // 生成JWT token
            String token = jwtUtil.generateAnchorToken(anchor.getId(), anchor.getUsername());

            // 返回结果（不包含密码）
            anchor.setPassword(null);
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("anchor", anchor);

            log.info("主播登录成功：anchorId={}, username={}", anchor.getId(), anchor.getUsername());
            return Result.success("登录成功", result);

        } catch (Exception e) {
            log.error("主播登录失败", e);
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 验证token
     */
    @PostMapping("/validate")
    public Result<Map<String, Object>> validateToken(@RequestHeader("Authorization") String authHeader) {
        try {
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return Result.unauthorized("无效的token格式");
            }

            String token = authHeader.substring(7);
            if (!jwtUtil.validateToken(token)) {
                return Result.unauthorized("token已过期或无效");
            }

            // 获取token中的信息
            Map<String, Object> result = new HashMap<>();
            Long userId = jwtUtil.getUserIdFromToken(token);
            Long anchorId = jwtUtil.getAnchorIdFromToken(token);

            if (userId != null) {
                User user = userService.getById(userId);
                if (user != null && user.getStatus() == 1) {
                    result.put("type", "user");
                    result.put("user", user);
                    return Result.success("token有效", result);
                }
            }

            if (anchorId != null) {
                Anchor anchor = anchorService.getById(anchorId);
                if (anchor != null && anchor.getStatus() == 1) {
                    anchor.setPassword(null); // 不返回密码
                    result.put("type", "anchor");
                    result.put("anchor", anchor);
                    return Result.success("token有效", result);
                }
            }

            return Result.unauthorized("用户不存在或已被禁用");

        } catch (Exception e) {
            log.error("验证token失败", e);
            return Result.unauthorized("token验证失败");
        }
    }
}
