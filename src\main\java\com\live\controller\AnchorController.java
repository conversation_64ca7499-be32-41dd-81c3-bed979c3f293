package com.live.controller;

import com.live.common.Result;
import com.live.entity.Anchor;
import com.live.entity.LiveRoom;
import com.live.service.AliyunLiveService;
import com.live.service.AnchorService;
import com.live.service.LiveRoomService;
import com.live.util.JwtUtil;
import com.live.websocket.LiveWebSocketServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 主播端控制器
 */
@Slf4j
@RestController
@RequestMapping("/anchor")
@RequiredArgsConstructor
public class AnchorController {

    private final AnchorService anchorService;
    private final LiveRoomService liveRoomService;
    private final AliyunLiveService aliyunLiveService;
    private final JwtUtil jwtUtil;

    /**
     * 获取主播信息
     */
    @GetMapping("/info")
    public Result<Anchor> getAnchorInfo(@RequestHeader("Authorization") String authHeader) {
        try {
            String token = authHeader.substring(7);
            Long anchorId = jwtUtil.getAnchorIdFromToken(token);
            if (anchorId == null) {
                return Result.unauthorized("主播未登录");
            }

            Anchor anchor = anchorService.getById(anchorId);
            if (anchor == null) {
                return Result.notFound("主播不存在");
            }

            // 不返回密码
            anchor.setPassword(null);
            return Result.success(anchor);

        } catch (Exception e) {
            log.error("获取主播信息失败", e);
            return Result.error("获取主播信息失败");
        }
    }

    /**
     * 获取主播的直播间列表
     */
    @GetMapping("/rooms")
    public Result<List<LiveRoom>> getAnchorRooms(@RequestHeader("Authorization") String authHeader) {
        try {
            String token = authHeader.substring(7);
            Long anchorId = jwtUtil.getAnchorIdFromToken(token);
            if (anchorId == null) {
                return Result.unauthorized("主播未登录");
            }

            List<LiveRoom> rooms = liveRoomService.findByAnchorId(anchorId);
            return Result.success(rooms);

        } catch (Exception e) {
            log.error("获取主播直播间列表失败", e);
            return Result.error("获取主播直播间列表失败");
        }
    }

    /**
     * 创建直播间
     */
    @PostMapping("/room/create")
    public Result<LiveRoom> createRoom(
            @RequestBody Map<String, String> roomData,
            @RequestHeader("Authorization") String authHeader) {
        try {
            String token = authHeader.substring(7);
            Long anchorId = jwtUtil.getAnchorIdFromToken(token);
            if (anchorId == null) {
                return Result.unauthorized("主播未登录");
            }

            String title = roomData.get("title");
            String description = roomData.get("description");
            String coverUrl = roomData.get("coverUrl");

            if (title == null || title.trim().isEmpty()) {
                return Result.badRequest("直播间标题不能为空");
            }

            LiveRoom room = liveRoomService.createRoom(anchorId, title.trim(), description, coverUrl);
            return Result.success("创建直播间成功", room);

        } catch (Exception e) {
            log.error("创建直播间失败", e);
            return Result.error("创建直播间失败");
        }
    }

    /**
     * 获取开播链接
     */
    @PostMapping("/room/{roomId}/start-urls")
    public Result<Map<String, Object>> getStartUrls(
            @PathVariable Long roomId,
            @RequestHeader("Authorization") String authHeader) {
        try {
            String token = authHeader.substring(7);
            Long anchorId = jwtUtil.getAnchorIdFromToken(token);
            if (anchorId == null) {
                return Result.unauthorized("主播未登录");
            }

            LiveRoom room = liveRoomService.getById(roomId);
            if (room == null) {
                return Result.notFound("直播间不存在");
            }

            if (!room.getAnchorId().equals(anchorId)) {
                return Result.forbidden("无权限操作此直播间");
            }

            // 生成流名称
            String streamName = "stream_" + room.getRoomCode();

            // 生成推流地址
            String pushUrl = aliyunLiveService.generatePushUrl(streamName);

            // 生成播放地址
            Map<String, String> playUrls = aliyunLiveService.generatePlayUrls(streamName);

            Map<String, Object> result = new HashMap<>();
            result.put("pushUrl", pushUrl);
            result.put("playUrls", playUrls);
            result.put("streamName", streamName);

            return Result.success("获取开播链接成功", result);

        } catch (Exception e) {
            log.error("获取开播链接失败", e);
            return Result.error("获取开播链接失败");
        }
    }

    /**
     * 开始直播
     */
    @PostMapping("/room/{roomId}/start")
    public Result<Map<String, Object>> startLive(
            @PathVariable Long roomId,
            @RequestBody Map<String, String> startData,
            @RequestHeader("Authorization") String authHeader) {
        try {
            String token = authHeader.substring(7);
            Long anchorId = jwtUtil.getAnchorIdFromToken(token);
            if (anchorId == null) {
                return Result.unauthorized("主播未登录");
            }

            LiveRoom room = liveRoomService.getById(roomId);
            if (room == null) {
                return Result.notFound("直播间不存在");
            }

            if (!room.getAnchorId().equals(anchorId)) {
                return Result.forbidden("无权限操作此直播间");
            }

            String streamUrl = startData.get("streamUrl");
            String playUrl = startData.get("playUrl");

            if (streamUrl == null || streamUrl.trim().isEmpty()) {
                return Result.badRequest("推流地址不能为空");
            }

            // 开始直播
            boolean success = liveRoomService.startLive(roomId, streamUrl.trim(), playUrl);
            if (!success) {
                return Result.error("开播失败");
            }

            // 开始录制
            String streamName = "stream_" + room.getRoomCode();
            aliyunLiveService.startRecord(streamName);

            // 通过WebSocket通知房间用户直播开始
            LiveWebSocketServer.sendSystemMessageToRoom(room.getRoomCode(), "直播开始了！");

            Map<String, Object> result = new HashMap<>();
            result.put("room", liveRoomService.getById(roomId));

            log.info("主播开播成功：anchorId={}, roomId={}", anchorId, roomId);
            return Result.success("开播成功", result);

        } catch (Exception e) {
            log.error("开播失败", e);
            return Result.error("开播失败");
        }
    }

    /**
     * 结束直播
     */
    @PostMapping("/room/{roomId}/stop")
    public Result<Map<String, Object>> stopLive(
            @PathVariable Long roomId,
            @RequestHeader("Authorization") String authHeader) {
        try {
            String token = authHeader.substring(7);
            Long anchorId = jwtUtil.getAnchorIdFromToken(token);
            if (anchorId == null) {
                return Result.unauthorized("主播未登录");
            }

            LiveRoom room = liveRoomService.getById(roomId);
            if (room == null) {
                return Result.notFound("直播间不存在");
            }

            if (!room.getAnchorId().equals(anchorId)) {
                return Result.forbidden("无权限操作此直播间");
            }

            // 停止录制并获取录播地址
            String streamName = "stream_" + room.getRoomCode();
            String recordUrl = aliyunLiveService.stopRecord(streamName);

            // 结束直播
            boolean success = liveRoomService.stopLive(roomId, recordUrl);
            if (!success) {
                return Result.error("结束直播失败");
            }

            // 通过WebSocket通知房间用户直播结束
            LiveWebSocketServer.sendSystemMessageToRoom(room.getRoomCode(), "直播已结束，感谢观看！");

            Map<String, Object> result = new HashMap<>();
            result.put("room", liveRoomService.getById(roomId));

            log.info("主播结束直播成功：anchorId={}, roomId={}", anchorId, roomId);
            return Result.success("结束直播成功", result);

        } catch (Exception e) {
            log.error("结束直播失败", e);
            return Result.error("结束直播失败");
        }
    }

    /**
     * 更新直播间信息
     */
    @PutMapping("/room/{roomId}")
    public Result<LiveRoom> updateRoom(
            @PathVariable Long roomId,
            @RequestBody Map<String, String> roomData,
            @RequestHeader("Authorization") String authHeader) {
        try {
            String token = authHeader.substring(7);
            Long anchorId = jwtUtil.getAnchorIdFromToken(token);
            if (anchorId == null) {
                return Result.unauthorized("主播未登录");
            }

            LiveRoom room = liveRoomService.getById(roomId);
            if (room == null) {
                return Result.notFound("直播间不存在");
            }

            if (!room.getAnchorId().equals(anchorId)) {
                return Result.forbidden("无权限操作此直播间");
            }

            // 更新直播间信息
            String title = roomData.get("title");
            String description = roomData.get("description");
            String coverUrl = roomData.get("coverUrl");

            if (title != null && !title.trim().isEmpty()) {
                room.setTitle(title.trim());
            }
            if (description != null) {
                room.setDescription(description);
            }
            if (coverUrl != null) {
                room.setCoverUrl(coverUrl);
            }

            liveRoomService.updateById(room);
            return Result.success("更新直播间信息成功", room);

        } catch (Exception e) {
            log.error("更新直播间信息失败", e);
            return Result.error("更新直播间信息失败");
        }
    }

    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    public Result<Void> changePassword(
            @RequestBody Map<String, String> passwordData,
            @RequestHeader("Authorization") String authHeader) {
        try {
            String token = authHeader.substring(7);
            Long anchorId = jwtUtil.getAnchorIdFromToken(token);
            if (anchorId == null) {
                return Result.unauthorized("主播未登录");
            }

            String oldPassword = passwordData.get("oldPassword");
            String newPassword = passwordData.get("newPassword");

            if (oldPassword == null || oldPassword.trim().isEmpty()) {
                return Result.badRequest("原密码不能为空");
            }
            if (newPassword == null || newPassword.trim().isEmpty()) {
                return Result.badRequest("新密码不能为空");
            }
            if (newPassword.length() < 6) {
                return Result.badRequest("新密码长度不能少于6位");
            }

            boolean success = anchorService.updatePassword(anchorId, oldPassword, newPassword);
            if (!success) {
                return Result.error("原密码错误");
            }

            return Result.success("密码修改成功");

        } catch (Exception e) {
            log.error("修改密码失败", e);
            return Result.error("修改密码失败");
        }
    }
}
