package com.live.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.live.common.PageResult;
import com.live.common.Result;
import com.live.entity.Robot;
import com.live.entity.RobotMessage;
import com.live.entity.RoomRobotConfig;
import com.live.service.RobotAutoService;
import com.live.service.RobotMessageService;
import com.live.service.RobotService;
import com.live.service.RoomRobotConfigService;
import com.live.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 机器人管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/robot")
@RequiredArgsConstructor
public class RobotController {

    private final RobotService robotService;
    private final RobotMessageService robotMessageService;
    private final RoomRobotConfigService roomRobotConfigService;
    private final RobotAutoService robotAutoService;
    private final JwtUtil jwtUtil;

    /**
     * 获取所有可用机器人列表
     */
    @GetMapping("/list")
    public Result<List<Robot>> getRobotList() {
        try {
            List<Robot> robots = robotService.getAvailableRobots();
            return Result.success(robots);
        } catch (Exception e) {
            log.error("获取机器人列表失败", e);
            return Result.error("获取机器人列表失败");
        }
    }

    /**
     * 分页获取机器人列表
     */
    @GetMapping("/page")
    public Result<PageResult<Robot>> getRobotPage(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Page<Robot> page = new Page<>(current, size);
            IPage<Robot> result = robotService.page(page);
            
            PageResult<Robot> pageResult = PageResult.of(
                    result.getRecords(),
                    result.getTotal(),
                    result.getSize(),
                    result.getCurrent()
            );
            
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("分页获取机器人列表失败", e);
            return Result.error("分页获取机器人列表失败");
        }
    }

    /**
     * 获取推荐机器人
     */
    @GetMapping("/recommended")
    public Result<List<Robot>> getRecommendedRobots(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<Robot> robots = robotService.getRecommendedRobots(limit);
            return Result.success(robots);
        } catch (Exception e) {
            log.error("获取推荐机器人失败", e);
            return Result.error("获取推荐机器人失败");
        }
    }

    /**
     * 创建机器人
     */
    @PostMapping("/create")
    public Result<Robot> createRobot(@RequestBody Map<String, Object> robotData) {
        try {
            String nickname = (String) robotData.get("nickname");
            String avatarUrl = (String) robotData.get("avatarUrl");
            Integer gender = (Integer) robotData.get("gender");
            String personality = (String) robotData.get("personality");

            if (nickname == null || nickname.trim().isEmpty()) {
                return Result.badRequest("机器人昵称不能为空");
            }

            Robot robot = robotService.createRobot(nickname.trim(), avatarUrl, gender, personality);
            if (robot == null) {
                return Result.error("创建机器人失败，昵称可能已存在");
            }

            return Result.success("创建机器人成功", robot);
        } catch (Exception e) {
            log.error("创建机器人失败", e);
            return Result.error("创建机器人失败");
        }
    }

    /**
     * 更新机器人信息
     */
    @PutMapping("/{robotId}")
    public Result<Void> updateRobot(
            @PathVariable Long robotId,
            @RequestBody Map<String, Object> robotData) {
        try {
            String nickname = (String) robotData.get("nickname");
            String avatarUrl = (String) robotData.get("avatarUrl");
            Integer gender = (Integer) robotData.get("gender");
            String personality = (String) robotData.get("personality");

            if (nickname == null || nickname.trim().isEmpty()) {
                return Result.badRequest("机器人昵称不能为空");
            }

            boolean success = robotService.updateRobot(robotId, nickname.trim(), avatarUrl, gender, personality);
            if (!success) {
                return Result.error("更新机器人失败，昵称可能已存在或机器人不存在");
            }

            return Result.success("更新机器人成功");
        } catch (Exception e) {
            log.error("更新机器人失败", e);
            return Result.error("更新机器人失败");
        }
    }

    /**
     * 删除机器人
     */
    @DeleteMapping("/{robotId}")
    public Result<Void> deleteRobot(@PathVariable Long robotId) {
        try {
            boolean success = robotService.deleteRobot(robotId);
            if (!success) {
                return Result.error("删除机器人失败，机器人不存在");
            }

            return Result.success("删除机器人成功");
        } catch (Exception e) {
            log.error("删除机器人失败", e);
            return Result.error("删除机器人失败");
        }
    }

    /**
     * 获取机器人详情
     */
    @GetMapping("/{robotId}")
    public Result<Map<String, Object>> getRobotDetail(@PathVariable Long robotId) {
        try {
            Robot robot = robotService.getById(robotId);
            if (robot == null) {
                return Result.notFound("机器人不存在");
            }

            // 获取机器人的消息模板
            List<RobotMessage> messages = robotMessageService.getMessagesByRobotId(robotId);
            
            // 获取机器人的配置数量
            long configCount = roomRobotConfigService.getRobotConfigCount(robotId);

            Map<String, Object> result = new HashMap<>();
            result.put("robot", robot);
            result.put("messages", messages);
            result.put("configCount", configCount);

            return Result.success(result);
        } catch (Exception e) {
            log.error("获取机器人详情失败", e);
            return Result.error("获取机器人详情失败");
        }
    }

    /**
     * 获取机器人消息模板
     */
    @GetMapping("/{robotId}/messages")
    public Result<List<RobotMessage>> getRobotMessages(@PathVariable Long robotId) {
        try {
            List<RobotMessage> messages = robotMessageService.getMessagesByRobotId(robotId);
            return Result.success(messages);
        } catch (Exception e) {
            log.error("获取机器人消息模板失败", e);
            return Result.error("获取机器人消息模板失败");
        }
    }

    /**
     * 为机器人添加消息模板
     */
    @PostMapping("/{robotId}/messages")
    public Result<RobotMessage> addRobotMessage(
            @PathVariable Long robotId,
            @RequestBody Map<String, Object> messageData) {
        try {
            Integer messageType = (Integer) messageData.get("messageType");
            String content = (String) messageData.get("content");
            String triggerCondition = (String) messageData.get("triggerCondition");
            Integer weight = (Integer) messageData.get("weight");

            if (content == null || content.trim().isEmpty()) {
                return Result.badRequest("消息内容不能为空");
            }

            RobotMessage message = robotMessageService.createMessage(
                    robotId, messageType, content.trim(), triggerCondition, weight);

            return Result.success("添加消息模板成功", message);
        } catch (Exception e) {
            log.error("添加机器人消息模板失败", e);
            return Result.error("添加机器人消息模板失败");
        }
    }

    /**
     * 更新机器人消息模板
     */
    @PutMapping("/messages/{messageId}")
    public Result<Void> updateRobotMessage(
            @PathVariable Long messageId,
            @RequestBody Map<String, Object> messageData) {
        try {
            Integer messageType = (Integer) messageData.get("messageType");
            String content = (String) messageData.get("content");
            String triggerCondition = (String) messageData.get("triggerCondition");
            Integer weight = (Integer) messageData.get("weight");

            boolean success = robotMessageService.updateMessage(
                    messageId, messageType, content, triggerCondition, weight);
            if (!success) {
                return Result.error("更新消息模板失败，消息不存在");
            }

            return Result.success("更新消息模板成功");
        } catch (Exception e) {
            log.error("更新机器人消息模板失败", e);
            return Result.error("更新机器人消息模板失败");
        }
    }

    /**
     * 删除机器人消息模板
     */
    @DeleteMapping("/messages/{messageId}")
    public Result<Void> deleteRobotMessage(@PathVariable Long messageId) {
        try {
            boolean success = robotMessageService.deleteMessage(messageId);
            if (!success) {
                return Result.error("删除消息模板失败，消息不存在");
            }

            return Result.success("删除消息模板成功");
        } catch (Exception e) {
            log.error("删除机器人消息模板失败", e);
            return Result.error("删除机器人消息模板失败");
        }
    }

    /**
     * 批量添加机器人消息模板
     */
    @PostMapping("/{robotId}/messages/batch")
    public Result<Map<String, Object>> batchAddRobotMessages(
            @PathVariable Long robotId,
            @RequestBody Map<String, Object> batchData) {
        try {
            @SuppressWarnings("unchecked")
            List<String> contents = (List<String>) batchData.get("contents");
            String triggerCondition = (String) batchData.get("triggerCondition");

            if (contents == null || contents.isEmpty()) {
                return Result.badRequest("消息内容列表不能为空");
            }

            int count = robotMessageService.batchCreateMessages(robotId, contents, triggerCondition);

            Map<String, Object> result = new HashMap<>();
            result.put("count", count);

            return Result.success("批量添加消息模板成功", result);
        } catch (Exception e) {
            log.error("批量添加机器人消息模板失败", e);
            return Result.error("批量添加机器人消息模板失败");
        }
    }

    /**
     * 手动发送机器人消息
     */
    @PostMapping("/send")
    public Result<Void> manualSendMessage(@RequestBody Map<String, Object> sendData) {
        try {
            Long roomId = Long.valueOf(sendData.get("roomId").toString());
            Long robotId = Long.valueOf(sendData.get("robotId").toString());
            String content = (String) sendData.get("content");

            if (content == null || content.trim().isEmpty()) {
                return Result.badRequest("消息内容不能为空");
            }

            boolean success = robotAutoService.manualSendRobotMessage(roomId, robotId, content.trim());
            if (!success) {
                return Result.error("发送消息失败");
            }

            return Result.success("发送消息成功");
        } catch (Exception e) {
            log.error("手动发送机器人消息失败", e);
            return Result.error("手动发送机器人消息失败");
        }
    }
}
