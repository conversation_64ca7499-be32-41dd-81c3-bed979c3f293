package com.live.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.live.entity.Robot;
import com.live.mapper.RobotMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

/**
 * 机器人服务
 */
@Slf4j
@Service
public class RobotService extends ServiceImpl<RobotMapper, Robot> {

    /**
     * 根据机器人编码查找机器人
     */
    public Robot findByRobotCode(String robotCode) {
        return this.getOne(new LambdaQueryWrapper<Robot>()
                .eq(Robot::getRobotCode, robotCode));
    }

    /**
     * 获取所有可用的机器人
     */
    public List<Robot> getAvailableRobots() {
        return this.list(new LambdaQueryWrapper<Robot>()
                .eq(Robot::getStatus, 1)
                .orderByDesc(Robot::getUsageFrequency));
    }

    /**
     * 创建机器人
     */
    public Robot createRobot(String nickname, String avatarUrl, Integer gender, String personality) {
        // 检查昵称是否已存在
        Robot existingRobot = this.getOne(new LambdaQueryWrapper<Robot>()
                .eq(Robot::getNickname, nickname));
        if (existingRobot != null) {
            log.warn("创建机器人失败：昵称已存在 - {}", nickname);
            return null;
        }

        Robot robot = new Robot();
        robot.setRobotCode(generateRobotCode());
        robot.setNickname(nickname);
        robot.setAvatarUrl(avatarUrl);
        robot.setGender(gender);
        robot.setPersonality(personality);
        robot.setUsageCount(0);
        robot.setUsageFrequency(BigDecimal.ZERO);
        robot.setStatus(1);

        this.save(robot);
        log.info("创建机器人成功：robotId={}, nickname={}", robot.getId(), nickname);
        return robot;
    }

    /**
     * 更新机器人信息
     */
    public boolean updateRobot(Long robotId, String nickname, String avatarUrl, Integer gender, String personality) {
        Robot robot = this.getById(robotId);
        if (robot == null) {
            return false;
        }

        // 检查昵称是否被其他机器人使用
        if (!robot.getNickname().equals(nickname)) {
            Robot existingRobot = this.getOne(new LambdaQueryWrapper<Robot>()
                    .eq(Robot::getNickname, nickname)
                    .ne(Robot::getId, robotId));
            if (existingRobot != null) {
                log.warn("更新机器人失败：昵称已存在 - {}", nickname);
                return false;
            }
        }

        robot.setNickname(nickname);
        robot.setAvatarUrl(avatarUrl);
        robot.setGender(gender);
        robot.setPersonality(personality);

        boolean result = this.updateById(robot);
        if (result) {
            log.info("更新机器人成功：robotId={}, nickname={}", robotId, nickname);
        }
        return result;
    }

    /**
     * 更新机器人状态
     */
    public boolean updateRobotStatus(Long robotId, Integer status) {
        Robot robot = this.getById(robotId);
        if (robot == null) {
            return false;
        }

        robot.setStatus(status);
        boolean result = this.updateById(robot);
        if (result) {
            log.info("更新机器人状态成功：robotId={}, status={}", robotId, status);
        }
        return result;
    }

    /**
     * 增加机器人使用次数
     */
    public void incrementUsageCount(Long robotId) {
        Robot robot = this.getById(robotId);
        if (robot != null) {
            robot.setUsageCount(robot.getUsageCount() + 1);
            
            // 重新计算使用频率（这里简单地用使用次数除以100作为频率）
            BigDecimal frequency = BigDecimal.valueOf(robot.getUsageCount()).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
            robot.setUsageFrequency(frequency);
            
            this.updateById(robot);
        }
    }

    /**
     * 批量更新机器人使用频率
     */
    public void updateUsageFrequencies() {
        List<Robot> robots = this.list();
        for (Robot robot : robots) {
            // 根据使用次数计算频率
            BigDecimal frequency = BigDecimal.valueOf(robot.getUsageCount()).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
            robot.setUsageFrequency(frequency);
            this.updateById(robot);
        }
        log.info("批量更新机器人使用频率完成，共更新{}个机器人", robots.size());
    }

    /**
     * 根据使用频率获取推荐机器人
     */
    public List<Robot> getRecommendedRobots(int limit) {
        return this.list(new LambdaQueryWrapper<Robot>()
                .eq(Robot::getStatus, 1)
                .orderByDesc(Robot::getUsageFrequency)
                .orderByDesc(Robot::getUsageCount)
                .last("LIMIT " + limit));
    }

    /**
     * 删除机器人
     */
    public boolean deleteRobot(Long robotId) {
        Robot robot = this.getById(robotId);
        if (robot == null) {
            return false;
        }

        // 软删除：设置状态为禁用
        robot.setStatus(0);
        boolean result = this.updateById(robot);
        if (result) {
            log.info("删除机器人成功：robotId={}, nickname={}", robotId, robot.getNickname());
        }
        return result;
    }

    /**
     * 生成机器人编码
     */
    private String generateRobotCode() {
        return "ROBOT_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
