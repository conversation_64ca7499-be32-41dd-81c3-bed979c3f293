package com.live.websocket;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.live.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 直播WebSocket服务器
 */
@Slf4j
@Component
@ServerEndpoint("/websocket/live/{roomCode}/{token}")
public class LiveWebSocketServer {

    // 静态变量，用来记录当前在线连接数
    private static int onlineCount = 0;
    
    // concurrent包的线程安全Set，用来存放每个客户端对应的WebSocket对象
    private static ConcurrentHashMap<String, CopyOnWriteArraySet<LiveWebSocketServer>> roomClients = new ConcurrentHashMap<>();
    
    // 与某个客户端的连接会话，需要通过它来给客户端发送数据
    private Session session;
    
    // 房间编码
    private String roomCode;
    
    // 用户ID
    private Long userId;
    
    // JWT工具类（需要静态注入）
    private static JwtUtil jwtUtil;

    @Autowired
    public void setJwtUtil(JwtUtil jwtUtil) {
        LiveWebSocketServer.jwtUtil = jwtUtil;
    }

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("roomCode") String roomCode, @PathParam("token") String token) {
        try {
            // 验证token
            if (!jwtUtil.validateToken(token)) {
                log.warn("WebSocket连接失败：token无效 - roomCode={}", roomCode);
                session.close();
                return;
            }

            // 获取用户ID
            Long userId = jwtUtil.getUserIdFromToken(token);
            if (userId == null) {
                log.warn("WebSocket连接失败：无法获取用户ID - roomCode={}", roomCode);
                session.close();
                return;
            }

            this.session = session;
            this.roomCode = roomCode;
            this.userId = userId;

            // 加入房间
            roomClients.computeIfAbsent(roomCode, k -> new CopyOnWriteArraySet<>()).add(this);
            
            addOnlineCount();
            log.info("用户连接WebSocket成功：userId={}, roomCode={}, 当前在线人数={}", userId, roomCode, getOnlineCount());

            // 发送连接成功消息
            JSONObject message = new JSONObject();
            message.put("type", "connect");
            message.put("message", "连接成功");
            message.put("onlineCount", getOnlineCount());
            sendMessage(message.toJSONString());

        } catch (Exception e) {
            log.error("WebSocket连接异常", e);
            try {
                session.close();
            } catch (IOException ex) {
                log.error("关闭WebSocket连接失败", ex);
            }
        }
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        // 从房间中移除
        CopyOnWriteArraySet<LiveWebSocketServer> roomSet = roomClients.get(roomCode);
        if (roomSet != null) {
            roomSet.remove(this);
            if (roomSet.isEmpty()) {
                roomClients.remove(roomCode);
            }
        }
        
        subOnlineCount();
        log.info("用户断开WebSocket连接：userId={}, roomCode={}, 当前在线人数={}", userId, roomCode, getOnlineCount());
    }

    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        try {
            log.info("收到WebSocket消息：userId={}, roomCode={}, message={}", userId, roomCode, message);
            
            JSONObject messageObj = JSON.parseObject(message);
            String type = messageObj.getString("type");
            
            switch (type) {
                case "heartbeat":
                    // 心跳消息
                    JSONObject heartbeatResponse = new JSONObject();
                    heartbeatResponse.put("type", "heartbeat");
                    heartbeatResponse.put("timestamp", System.currentTimeMillis());
                    sendMessage(heartbeatResponse.toJSONString());
                    break;
                    
                case "comment":
                    // 评论消息（这里只是转发，实际保存在Controller中处理）
                    broadcastToRoom(roomCode, message);
                    break;
                    
                default:
                    log.warn("未知的消息类型：{}", type);
                    break;
            }
            
        } catch (Exception e) {
            log.error("处理WebSocket消息异常", e);
        }
    }

    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("WebSocket发生错误：userId={}, roomCode={}", userId, roomCode, error);
    }

    /**
     * 实现服务器主动推送
     */
    public void sendMessage(String message) {
        try {
            this.session.getBasicRemote().sendText(message);
        } catch (IOException e) {
            log.error("发送WebSocket消息失败", e);
        }
    }

    /**
     * 群发自定义消息到指定房间
     */
    public static void broadcastToRoom(String roomCode, String message) {
        CopyOnWriteArraySet<LiveWebSocketServer> roomSet = roomClients.get(roomCode);
        if (roomSet != null) {
            for (LiveWebSocketServer client : roomSet) {
                try {
                    client.sendMessage(message);
                } catch (Exception e) {
                    log.error("向房间{}广播消息失败", roomCode, e);
                }
            }
        }
    }

    /**
     * 发送评论消息到房间
     */
    public static void sendCommentToRoom(String roomCode, JSONObject comment) {
        JSONObject message = new JSONObject();
        message.put("type", "comment");
        message.put("data", comment);
        broadcastToRoom(roomCode, message.toJSONString());
    }

    /**
     * 发送系统消息到房间
     */
    public static void sendSystemMessageToRoom(String roomCode, String content) {
        JSONObject message = new JSONObject();
        message.put("type", "system");
        message.put("message", content);
        message.put("timestamp", System.currentTimeMillis());
        broadcastToRoom(roomCode, message.toJSONString());
    }

    /**
     * 发送观看人数更新到房间
     */
    public static void sendViewerCountToRoom(String roomCode, int count) {
        JSONObject message = new JSONObject();
        message.put("type", "viewerCount");
        message.put("count", count);
        message.put("timestamp", System.currentTimeMillis());
        broadcastToRoom(roomCode, message.toJSONString());
    }

    /**
     * 获取房间在线人数
     */
    public static int getRoomOnlineCount(String roomCode) {
        CopyOnWriteArraySet<LiveWebSocketServer> roomSet = roomClients.get(roomCode);
        return roomSet != null ? roomSet.size() : 0;
    }

    public static synchronized int getOnlineCount() {
        return onlineCount;
    }

    public static synchronized void addOnlineCount() {
        LiveWebSocketServer.onlineCount++;
    }

    public static synchronized void subOnlineCount() {
        LiveWebSocketServer.onlineCount--;
    }
}
