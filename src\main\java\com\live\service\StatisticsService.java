package com.live.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.live.entity.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 统计服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StatisticsService {

    private final UserService userService;
    private final AnchorService anchorService;
    private final LiveRoomService liveRoomService;
    private final CommentService commentService;
    private final UserWatchRecordService watchRecordService;
    private final RobotService robotService;

    /**
     * 获取系统总览统计
     */
    public Map<String, Object> getSystemOverview() {
        Map<String, Object> overview = new HashMap<>();

        // 用户统计
        long totalUsers = userService.count();
        long todayNewUsers = getTodayNewUsers();
        overview.put("totalUsers", totalUsers);
        overview.put("todayNewUsers", todayNewUsers);

        // 主播统计
        long totalAnchors = anchorService.count();
        long activeAnchors = getActiveAnchors();
        overview.put("totalAnchors", totalAnchors);
        overview.put("activeAnchors", activeAnchors);

        // 直播间统计
        long totalRooms = liveRoomService.count();
        long liveRooms = getLiveRoomsCount();
        overview.put("totalRooms", totalRooms);
        overview.put("liveRooms", liveRooms);

        // 机器人统计
        long totalRobots = robotService.count();
        long activeRobots = getActiveRobots();
        overview.put("totalRobots", totalRobots);
        overview.put("activeRobots", activeRobots);

        // 评论统计
        long totalComments = commentService.count();
        long todayComments = getTodayComments();
        overview.put("totalComments", totalComments);
        overview.put("todayComments", todayComments);

        return overview;
    }

    /**
     * 获取用户统计
     */
    public Map<String, Object> getUserStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // 总用户数
        long totalUsers = userService.count();
        stats.put("totalUsers", totalUsers);

        // 今日新增用户
        long todayNewUsers = getTodayNewUsers();
        stats.put("todayNewUsers", todayNewUsers);

        // 本周新增用户
        long weekNewUsers = getWeekNewUsers();
        stats.put("weekNewUsers", weekNewUsers);

        // 本月新增用户
        long monthNewUsers = getMonthNewUsers();
        stats.put("monthNewUsers", monthNewUsers);

        // 活跃用户（最近7天有观看记录）
        long activeUsers = getActiveUsers(7);
        stats.put("activeUsers", activeUsers);

        // 用户性别分布
        Map<String, Long> genderDistribution = getUserGenderDistribution();
        stats.put("genderDistribution", genderDistribution);

        return stats;
    }

    /**
     * 获取直播统计
     */
    public Map<String, Object> getLiveStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // 总直播间数
        long totalRooms = liveRoomService.count();
        stats.put("totalRooms", totalRooms);

        // 正在直播的房间数
        long liveRooms = getLiveRoomsCount();
        stats.put("liveRooms", liveRooms);

        // 今日开播次数
        long todayLives = getTodayLives();
        stats.put("todayLives", todayLives);

        // 总观看时长（小时）
        long totalWatchHours = getTotalWatchHours();
        stats.put("totalWatchHours", totalWatchHours);

        // 平均观看时长（分钟）
        double avgWatchMinutes = getAverageWatchMinutes();
        stats.put("avgWatchMinutes", avgWatchMinutes);

        // 最受欢迎的直播间（按观看人数）
        List<LiveRoom> popularRooms = getPopularRooms(10);
        stats.put("popularRooms", popularRooms);

        return stats;
    }

    /**
     * 获取机器人统计
     */
    public Map<String, Object> getRobotStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // 总机器人数
        long totalRobots = robotService.count();
        stats.put("totalRobots", totalRobots);

        // 活跃机器人数
        long activeRobots = getActiveRobots();
        stats.put("activeRobots", activeRobots);

        // 机器人评论总数
        long robotComments = getRobotCommentsCount();
        stats.put("robotComments", robotComments);

        // 今日机器人评论数
        long todayRobotComments = getTodayRobotComments();
        stats.put("todayRobotComments", todayRobotComments);

        // 最活跃的机器人
        List<Robot> activeRobotList = robotService.getRecommendedRobots(10);
        stats.put("activeRobotList", activeRobotList);

        return stats;
    }

    /**
     * 获取评论统计
     */
    public Map<String, Object> getCommentStatistics() {
        Map<String, Object> stats = new HashMap<>();

        // 总评论数
        long totalComments = commentService.count();
        stats.put("totalComments", totalComments);

        // 今日评论数
        long todayComments = getTodayComments();
        stats.put("todayComments", todayComments);

        // 用户评论数
        long userComments = getUserCommentsCount();
        stats.put("userComments", userComments);

        // 机器人评论数
        long robotComments = getRobotCommentsCount();
        stats.put("robotComments", robotComments);

        // 评论类型分布
        Map<String, Long> typeDistribution = getCommentTypeDistribution();
        stats.put("typeDistribution", typeDistribution);

        return stats;
    }

    // 私有方法实现具体统计逻辑

    private long getTodayNewUsers() {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        return userService.count(new LambdaQueryWrapper<User>()
                .ge(User::getCreatedTime, startOfDay));
    }

    private long getWeekNewUsers() {
        LocalDateTime startOfWeek = LocalDate.now().minusDays(7).atStartOfDay();
        return userService.count(new LambdaQueryWrapper<User>()
                .ge(User::getCreatedTime, startOfWeek));
    }

    private long getMonthNewUsers() {
        LocalDateTime startOfMonth = LocalDate.now().minusDays(30).atStartOfDay();
        return userService.count(new LambdaQueryWrapper<User>()
                .ge(User::getCreatedTime, startOfMonth));
    }

    private long getActiveUsers(int days) {
        LocalDateTime since = LocalDateTime.now().minusDays(days);
        // 这里需要根据观看记录统计活跃用户，简化实现
        return userService.count(new LambdaQueryWrapper<User>()
                .ge(User::getLastLoginTime, since));
    }

    private Map<String, Long> getUserGenderDistribution() {
        Map<String, Long> distribution = new HashMap<>();
        distribution.put("male", userService.count(new LambdaQueryWrapper<User>().eq(User::getGender, 1)));
        distribution.put("female", userService.count(new LambdaQueryWrapper<User>().eq(User::getGender, 2)));
        distribution.put("unknown", userService.count(new LambdaQueryWrapper<User>().eq(User::getGender, 0)));
        return distribution;
    }

    private long getActiveAnchors() {
        return anchorService.count(new LambdaQueryWrapper<Anchor>()
                .eq(Anchor::getStatus, 1));
    }

    private long getLiveRoomsCount() {
        return liveRoomService.count(new LambdaQueryWrapper<LiveRoom>()
                .eq(LiveRoom::getStatus, 1));
    }

    private long getTodayLives() {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        return liveRoomService.count(new LambdaQueryWrapper<LiveRoom>()
                .ge(LiveRoom::getStartTime, startOfDay));
    }

    private long getTotalWatchHours() {
        // 这里需要从观看记录表统计，简化实现
        return watchRecordService.count() * 30 / 60; // 假设平均观看30分钟
    }

    private double getAverageWatchMinutes() {
        // 这里需要计算平均观看时长，简化实现
        return 25.5; // 假设平均25.5分钟
    }

    private List<LiveRoom> getPopularRooms(int limit) {
        return liveRoomService.list(new LambdaQueryWrapper<LiveRoom>()
                .orderByDesc(LiveRoom::getMaxViewerCount)
                .last("LIMIT " + limit));
    }

    private long getActiveRobots() {
        return robotService.count(new LambdaQueryWrapper<Robot>()
                .eq(Robot::getStatus, 1));
    }

    private long getRobotCommentsCount() {
        return commentService.count(new LambdaQueryWrapper<Comment>()
                .eq(Comment::getIsRobot, 1));
    }

    private long getTodayRobotComments() {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        return commentService.count(new LambdaQueryWrapper<Comment>()
                .eq(Comment::getIsRobot, 1)
                .ge(Comment::getCreatedTime, startOfDay));
    }

    private long getTodayComments() {
        LocalDateTime startOfDay = LocalDate.now().atStartOfDay();
        return commentService.count(new LambdaQueryWrapper<Comment>()
                .ge(Comment::getCreatedTime, startOfDay));
    }

    private long getUserCommentsCount() {
        return commentService.count(new LambdaQueryWrapper<Comment>()
                .eq(Comment::getIsRobot, 0));
    }

    private Map<String, Long> getCommentTypeDistribution() {
        Map<String, Long> distribution = new HashMap<>();
        distribution.put("text", commentService.count(new LambdaQueryWrapper<Comment>().eq(Comment::getCommentType, 1)));
        distribution.put("emoji", commentService.count(new LambdaQueryWrapper<Comment>().eq(Comment::getCommentType, 2)));
        distribution.put("image", commentService.count(new LambdaQueryWrapper<Comment>().eq(Comment::getCommentType, 3)));
        return distribution;
    }
}
