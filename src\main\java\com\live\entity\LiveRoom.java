package com.live.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 直播间实体类
 */
@Data
@TableName("live_rooms")
public class LiveRoom {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("room_code")
    private String roomCode;
    
    @TableField("anchor_id")
    private Long anchorId;
    
    @TableField("title")
    private String title;
    
    @TableField("description")
    private String description;
    
    @TableField("cover_url")
    private String coverUrl;
    
    @TableField("stream_url")
    private String streamUrl;
    
    @TableField("play_url")
    private String playUrl;
    
    @TableField("record_url")
    private String recordUrl;
    
    @TableField("status")
    private Integer status;
    
    @TableField("viewer_count")
    private Integer viewerCount;
    
    @TableField("max_viewer_count")
    private Integer maxViewerCount;
    
    @TableField("start_time")
    private LocalDateTime startTime;
    
    @TableField("end_time")
    private LocalDateTime endTime;
    
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
}
