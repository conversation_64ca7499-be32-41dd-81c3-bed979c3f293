package com.live.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.live.entity.UserWatchRecord;
import com.live.mapper.UserWatchRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 用户观看记录服务
 */
@Slf4j
@Service
public class UserWatchRecordService extends ServiceImpl<UserWatchRecordMapper, UserWatchRecord> {

    /**
     * 记录用户进入直播间
     */
    public UserWatchRecord recordEnter(Long userId, Long roomId, String ipAddress, String userAgent) {
        UserWatchRecord record = new UserWatchRecord();
        record.setUserId(userId);
        record.setRoomId(roomId);
        record.setEnterTime(LocalDateTime.now());
        record.setIpAddress(ipAddress);
        record.setUserAgent(userAgent);
        record.setWatchDuration(0);

        this.save(record);
        log.info("记录用户进入直播间：userId={}, roomId={}, recordId={}", userId, roomId, record.getId());
        return record;
    }

    /**
     * 记录用户离开直播间
     */
    public boolean recordLeave(Long recordId) {
        UserWatchRecord record = this.getById(recordId);
        if (record == null || record.getLeaveTime() != null) {
            return false;
        }

        LocalDateTime leaveTime = LocalDateTime.now();
        record.setLeaveTime(leaveTime);
        
        // 计算观看时长（秒）
        long duration = ChronoUnit.SECONDS.between(record.getEnterTime(), leaveTime);
        record.setWatchDuration((int) duration);

        boolean result = this.updateById(record);
        if (result) {
            log.info("记录用户离开直播间：userId={}, roomId={}, duration={}秒", 
                    record.getUserId(), record.getRoomId(), duration);
        }
        return result;
    }

    /**
     * 根据用户和房间查找最新的观看记录
     */
    public UserWatchRecord findLatestRecord(Long userId, Long roomId) {
        return this.getOne(new LambdaQueryWrapper<UserWatchRecord>()
                .eq(UserWatchRecord::getUserId, userId)
                .eq(UserWatchRecord::getRoomId, roomId)
                .orderByDesc(UserWatchRecord::getEnterTime)
                .last("LIMIT 1"));
    }

    /**
     * 获取用户在直播间的观看记录
     */
    public List<UserWatchRecord> getUserRoomRecords(Long userId, Long roomId) {
        return this.list(new LambdaQueryWrapper<UserWatchRecord>()
                .eq(UserWatchRecord::getUserId, userId)
                .eq(UserWatchRecord::getRoomId, roomId)
                .orderByDesc(UserWatchRecord::getEnterTime));
    }

    /**
     * 获取直播间的观看记录
     */
    public List<UserWatchRecord> getRoomRecords(Long roomId) {
        return this.list(new LambdaQueryWrapper<UserWatchRecord>()
                .eq(UserWatchRecord::getRoomId, roomId)
                .orderByDesc(UserWatchRecord::getEnterTime));
    }

    /**
     * 获取用户总观看时长（秒）
     */
    public long getUserTotalWatchDuration(Long userId) {
        List<UserWatchRecord> records = this.list(new LambdaQueryWrapper<UserWatchRecord>()
                .eq(UserWatchRecord::getUserId, userId)
                .isNotNull(UserWatchRecord::getLeaveTime));

        return records.stream()
                .mapToLong(record -> record.getWatchDuration() != null ? record.getWatchDuration() : 0)
                .sum();
    }

    /**
     * 获取直播间总观看时长（秒）
     */
    public long getRoomTotalWatchDuration(Long roomId) {
        List<UserWatchRecord> records = this.list(new LambdaQueryWrapper<UserWatchRecord>()
                .eq(UserWatchRecord::getRoomId, roomId)
                .isNotNull(UserWatchRecord::getLeaveTime));

        return records.stream()
                .mapToLong(record -> record.getWatchDuration() != null ? record.getWatchDuration() : 0)
                .sum();
    }

    /**
     * 获取直播间独立观看用户数
     */
    public long getRoomUniqueViewerCount(Long roomId) {
        return this.baseMapper.selectCount(new LambdaQueryWrapper<UserWatchRecord>()
                .eq(UserWatchRecord::getRoomId, roomId)
                .groupBy(UserWatchRecord::getUserId));
    }

    /**
     * 清理未完成的观看记录（用户异常退出等情况）
     */
    public void cleanupIncompleteRecords(int hoursAgo) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(hoursAgo);
        
        List<UserWatchRecord> incompleteRecords = this.list(new LambdaQueryWrapper<UserWatchRecord>()
                .isNull(UserWatchRecord::getLeaveTime)
                .lt(UserWatchRecord::getEnterTime, cutoffTime));

        for (UserWatchRecord record : incompleteRecords) {
            record.setLeaveTime(record.getEnterTime().plusHours(1)); // 假设观看了1小时
            long duration = ChronoUnit.SECONDS.between(record.getEnterTime(), record.getLeaveTime());
            record.setWatchDuration((int) duration);
            this.updateById(record);
        }

        if (!incompleteRecords.isEmpty()) {
            log.info("清理了{}条未完成的观看记录", incompleteRecords.size());
        }
    }
}
