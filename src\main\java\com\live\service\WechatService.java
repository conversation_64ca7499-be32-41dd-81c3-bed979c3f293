package com.live.service;

import com.alibaba.fastjson.JSONObject;
import com.live.entity.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 微信服务
 */
@Slf4j
@Service
public class WechatService {

    @Value("${live.wechat.app-id}")
    private String appId;

    @Value("${live.wechat.app-secret}")
    private String appSecret;

    @Value("${live.wechat.redirect-uri}")
    private String redirectUri;

    /**
     * 获取微信授权URL
     */
    public String getAuthUrl(String state) {
        try {
            String encodedRedirectUri = URLEncoder.encode(redirectUri, StandardCharsets.UTF_8.toString());
            return String.format(
                "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_userinfo&state=%s#wechat_redirect",
                appId, encodedRedirectUri, state
            );
        } catch (Exception e) {
            log.error("生成微信授权URL失败", e);
            throw new RuntimeException("生成微信授权URL失败");
        }
    }

    /**
     * 通过code获取access_token
     */
    public JSONObject getAccessToken(String code) {
        String url = String.format(
            "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code",
            appId, appSecret, code
        );

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            CloseableHttpResponse response = httpClient.execute(httpGet);
            String result = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (jsonObject.containsKey("errcode")) {
                log.error("获取微信access_token失败: {}", result);
                throw new RuntimeException("获取微信access_token失败: " + jsonObject.getString("errmsg"));
            }
            
            return jsonObject;
        } catch (IOException e) {
            log.error("请求微信API失败", e);
            throw new RuntimeException("请求微信API失败");
        }
    }

    /**
     * 获取用户信息
     */
    public JSONObject getUserInfo(String accessToken, String openid) {
        String url = String.format(
            "https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN",
            accessToken, openid
        );

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(url);
            CloseableHttpResponse response = httpClient.execute(httpGet);
            String result = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (jsonObject.containsKey("errcode")) {
                log.error("获取微信用户信息失败: {}", result);
                throw new RuntimeException("获取微信用户信息失败: " + jsonObject.getString("errmsg"));
            }
            
            return jsonObject;
        } catch (IOException e) {
            log.error("请求微信API失败", e);
            throw new RuntimeException("请求微信API失败");
        }
    }

    /**
     * 获取用户手机号
     */
    public String getUserPhone(String accessToken, String code) {
        String url = String.format(
            "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=%s",
            accessToken
        );

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 这里需要POST请求，传递code参数
            // 具体实现根据微信API文档调整
            // 注意：获取手机号需要小程序环境，网页授权无法直接获取手机号
            log.warn("网页授权无法直接获取手机号，需要通过其他方式获取");
            return null;
        } catch (Exception e) {
            log.error("获取用户手机号失败", e);
            return null;
        }
    }

    /**
     * 将微信用户信息转换为User实体
     */
    public User convertToUser(JSONObject userInfo) {
        User user = new User();
        user.setOpenid(userInfo.getString("openid"));
        user.setUnionid(userInfo.getString("unionid"));
        user.setNickname(userInfo.getString("nickname"));
        user.setAvatarUrl(userInfo.getString("headimgurl"));
        user.setGender(userInfo.getInteger("sex"));
        user.setCity(userInfo.getString("city"));
        user.setProvince(userInfo.getString("province"));
        user.setCountry(userInfo.getString("country"));
        user.setStatus(1); // 默认正常状态
        return user;
    }
}
