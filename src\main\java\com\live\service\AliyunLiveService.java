package com.live.service;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.live.model.v20161101.*;
import com.aliyuncs.profile.DefaultProfile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;

/**
 * 阿里云直播服务
 */
@Slf4j
@Service
public class AliyunLiveService {

    @Value("${live.aliyun.live.access-key-id}")
    private String accessKeyId;

    @Value("${live.aliyun.live.access-key-secret}")
    private String accessKeySecret;

    @Value("${live.aliyun.live.domain-name}")
    private String domainName;

    @Value("${live.aliyun.live.app-name}")
    private String appName;

    @Value("${live.aliyun.live.region-id}")
    private String regionId;

    @Value("${live.aliyun.live.push-auth-key}")
    private String pushAuthKey;

    @Value("${live.aliyun.live.play-auth-key}")
    private String playAuthKey;

    /**
     * 生成推流地址
     */
    public String generatePushUrl(String streamName) {
        try {
            // 推流地址格式：rtmp://domain/app/stream?auth_key=timestamp-rand-uid-md5hash
            String baseUrl = String.format("rtmp://%s/%s/%s", domainName, appName, streamName);
            
            // 生成鉴权参数
            long timestamp = LocalDateTime.now().plusHours(24).toEpochSecond(ZoneOffset.UTC); // 24小时后过期
            String rand = "0";
            String uid = "0";
            
            // 计算MD5
            String sstring = String.format("/%s/%s-%d-%s-%s-%s", appName, streamName, timestamp, rand, uid, pushAuthKey);
            String md5Hash = md5(sstring);
            
            String authKey = String.format("%d-%s-%s-%s", timestamp, rand, uid, md5Hash);
            return baseUrl + "?auth_key=" + authKey;
            
        } catch (Exception e) {
            log.error("生成推流地址失败", e);
            throw new RuntimeException("生成推流地址失败");
        }
    }

    /**
     * 生成播放地址
     */
    public Map<String, String> generatePlayUrls(String streamName) {
        try {
            Map<String, String> playUrls = new HashMap<>();
            
            // 生成鉴权参数
            long timestamp = LocalDateTime.now().plusHours(24).toEpochSecond(ZoneOffset.UTC);
            String rand = "0";
            String uid = "0";
            
            // RTMP播放地址
            String rtmpPath = String.format("/%s/%s", appName, streamName);
            String rtmpSstring = rtmpPath + "-" + timestamp + "-" + rand + "-" + uid + "-" + playAuthKey;
            String rtmpMd5 = md5(rtmpSstring);
            String rtmpAuthKey = String.format("%d-%s-%s-%s", timestamp, rand, uid, rtmpMd5);
            playUrls.put("rtmp", String.format("rtmp://%s%s?auth_key=%s", domainName, rtmpPath, rtmpAuthKey));
            
            // FLV播放地址
            String flvPath = String.format("/%s/%s.flv", appName, streamName);
            String flvSstring = flvPath + "-" + timestamp + "-" + rand + "-" + uid + "-" + playAuthKey;
            String flvMd5 = md5(flvSstring);
            String flvAuthKey = String.format("%d-%s-%s-%s", timestamp, rand, uid, flvMd5);
            playUrls.put("flv", String.format("http://%s%s?auth_key=%s", domainName, flvPath, flvAuthKey));
            
            // M3U8播放地址
            String m3u8Path = String.format("/%s/%s.m3u8", appName, streamName);
            String m3u8Sstring = m3u8Path + "-" + timestamp + "-" + rand + "-" + uid + "-" + playAuthKey;
            String m3u8Md5 = md5(m3u8Sstring);
            String m3u8AuthKey = String.format("%d-%s-%s-%s", timestamp, rand, uid, m3u8Md5);
            playUrls.put("m3u8", String.format("http://%s%s?auth_key=%s", domainName, m3u8Path, m3u8AuthKey));
            
            return playUrls;
            
        } catch (Exception e) {
            log.error("生成播放地址失败", e);
            throw new RuntimeException("生成播放地址失败");
        }
    }

    /**
     * 开始录制
     */
    public boolean startRecord(String streamName) {
        try {
            DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
            IAcsClient client = new DefaultAcsClient(profile);

            AddLiveRecordVideoComposeRequest request = new AddLiveRecordVideoComposeRequest();
            request.setDomainName(domainName);
            request.setAppName(appName);
            request.setStreamName(streamName);
            
            // 设置录制参数
            request.setOssEndpoint("oss-cn-hangzhou.aliyuncs.com"); // 根据实际情况修改
            request.setOssBucket("your-oss-bucket"); // 根据实际情况修改
            request.setOssObjectPrefix("live-records/");

            AddLiveRecordVideoComposeResponse response = client.getAcsResponse(request);
            log.info("开始录制成功：streamName={}, recordId={}", streamName, response.getRecordId());
            return true;
            
        } catch (Exception e) {
            log.error("开始录制失败：streamName={}", streamName, e);
            return false;
        }
    }

    /**
     * 停止录制
     */
    public String stopRecord(String streamName) {
        try {
            DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
            IAcsClient client = new DefaultAcsClient(profile);

            StopLiveStreamRecordRequest request = new StopLiveStreamRecordRequest();
            request.setDomainName(domainName);
            request.setAppName(appName);
            request.setStreamName(streamName);

            StopLiveStreamRecordResponse response = client.getAcsResponse(request);
            log.info("停止录制成功：streamName={}", streamName);
            
            // 这里应该返回录制文件的URL，具体实现根据阿里云API文档
            return "http://your-oss-domain.com/live-records/" + streamName + ".mp4";
            
        } catch (Exception e) {
            log.error("停止录制失败：streamName={}", streamName, e);
            return null;
        }
    }

    /**
     * 禁播流
     */
    public boolean forbidStream(String streamName) {
        try {
            DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
            IAcsClient client = new DefaultAcsClient(profile);

            ForbidLiveStreamRequest request = new ForbidLiveStreamRequest();
            request.setDomainName(domainName);
            request.setAppName(appName);
            request.setStreamName(streamName);
            request.setLiveStreamType("publisher");

            ForbidLiveStreamResponse response = client.getAcsResponse(request);
            log.info("禁播流成功：streamName={}", streamName);
            return true;
            
        } catch (Exception e) {
            log.error("禁播流失败：streamName={}", streamName, e);
            return false;
        }
    }

    /**
     * 恢复流
     */
    public boolean resumeStream(String streamName) {
        try {
            DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
            IAcsClient client = new DefaultAcsClient(profile);

            ResumeLiveStreamRequest request = new ResumeLiveStreamRequest();
            request.setDomainName(domainName);
            request.setAppName(appName);
            request.setStreamName(streamName);
            request.setLiveStreamType("publisher");

            ResumeLiveStreamResponse response = client.getAcsResponse(request);
            log.info("恢复流成功：streamName={}", streamName);
            return true;
            
        } catch (Exception e) {
            log.error("恢复流失败：streamName={}", streamName, e);
            return false;
        }
    }

    /**
     * MD5加密
     */
    private String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5加密失败", e);
        }
    }
}
