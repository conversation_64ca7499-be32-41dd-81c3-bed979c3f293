package com.live.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.live.common.PageResult;
import com.live.common.Result;
import com.live.entity.Comment;
import com.live.entity.LiveRoom;
import com.live.entity.UserWatchRecord;
import com.live.service.CommentService;
import com.live.service.LiveRoomService;
import com.live.service.UserWatchRecordService;
import com.live.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 观众端控制器
 */
@Slf4j
@RestController
@RequestMapping("/viewer")
@RequiredArgsConstructor
public class ViewerController {

    private final LiveRoomService liveRoomService;
    private final CommentService commentService;
    private final UserWatchRecordService watchRecordService;
    private final JwtUtil jwtUtil;

    /**
     * 获取正在直播的房间列表
     */
    @GetMapping("/rooms/live")
    public Result<List<LiveRoom>> getLiveRooms() {
        try {
            List<LiveRoom> rooms = liveRoomService.getLiveRooms();
            return Result.success(rooms);
        } catch (Exception e) {
            log.error("获取直播房间列表失败", e);
            return Result.error("获取直播房间列表失败");
        }
    }

    /**
     * 根据房间编码获取直播间信息
     */
    @GetMapping("/room/{roomCode}")
    public Result<LiveRoom> getRoomInfo(@PathVariable String roomCode) {
        try {
            LiveRoom room = liveRoomService.findByRoomCode(roomCode);
            if (room == null) {
                return Result.notFound("直播间不存在");
            }
            return Result.success(room);
        } catch (Exception e) {
            log.error("获取直播间信息失败", e);
            return Result.error("获取直播间信息失败");
        }
    }

    /**
     * 进入直播间
     */
    @PostMapping("/room/{roomCode}/enter")
    public Result<Map<String, Object>> enterRoom(
            @PathVariable String roomCode,
            @RequestHeader("Authorization") String authHeader,
            HttpServletRequest request) {
        try {
            // 验证token并获取用户ID
            String token = authHeader.substring(7);
            Long userId = jwtUtil.getUserIdFromToken(token);
            if (userId == null) {
                return Result.unauthorized("用户未登录");
            }

            // 检查直播间是否存在
            LiveRoom room = liveRoomService.findByRoomCode(roomCode);
            if (room == null) {
                return Result.notFound("直播间不存在");
            }

            // 记录用户进入直播间
            String ipAddress = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            UserWatchRecord record = watchRecordService.recordEnter(userId, room.getId(), ipAddress, userAgent);

            // 增加观看人数
            liveRoomService.incrementViewerCount(room.getId());

            // 返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("room", room);
            result.put("recordId", record.getId());

            log.info("用户进入直播间：userId={}, roomCode={}", userId, roomCode);
            return Result.success("进入直播间成功", result);

        } catch (Exception e) {
            log.error("进入直播间失败", e);
            return Result.error("进入直播间失败");
        }
    }

    /**
     * 离开直播间
     */
    @PostMapping("/room/leave")
    public Result<Void> leaveRoom(
            @RequestParam Long recordId,
            @RequestParam Long roomId) {
        try {
            // 记录用户离开直播间
            watchRecordService.recordLeave(recordId);

            // 减少观看人数
            liveRoomService.decrementViewerCount(roomId);

            log.info("用户离开直播间：recordId={}, roomId={}", recordId, roomId);
            return Result.success("离开直播间成功");

        } catch (Exception e) {
            log.error("离开直播间失败", e);
            return Result.error("离开直播间失败");
        }
    }

    /**
     * 发送评论
     */
    @PostMapping("/room/{roomCode}/comment")
    public Result<Comment> sendComment(
            @PathVariable String roomCode,
            @RequestBody Map<String, String> commentData,
            @RequestHeader("Authorization") String authHeader,
            HttpServletRequest request) {
        try {
            // 验证token并获取用户ID
            String token = authHeader.substring(7);
            Long userId = jwtUtil.getUserIdFromToken(token);
            if (userId == null) {
                return Result.unauthorized("用户未登录");
            }

            // 检查直播间是否存在
            LiveRoom room = liveRoomService.findByRoomCode(roomCode);
            if (room == null) {
                return Result.notFound("直播间不存在");
            }

            String content = commentData.get("content");
            if (content == null || content.trim().isEmpty()) {
                return Result.badRequest("评论内容不能为空");
            }

            // 发送评论
            String ipAddress = getClientIpAddress(request);
            Comment comment = commentService.sendUserComment(room.getId(), userId, content.trim(), ipAddress);

            return Result.success("评论发送成功", comment);

        } catch (Exception e) {
            log.error("发送评论失败", e);
            return Result.error("发送评论失败");
        }
    }

    /**
     * 获取直播间评论列表
     */
    @GetMapping("/room/{roomCode}/comments")
    public Result<PageResult<Comment>> getRoomComments(
            @PathVariable String roomCode,
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "20") int size) {
        try {
            // 检查直播间是否存在
            LiveRoom room = liveRoomService.findByRoomCode(roomCode);
            if (room == null) {
                return Result.notFound("直播间不存在");
            }

            // 获取评论列表
            IPage<Comment> page = commentService.getRoomComments(room.getId(), current, size);
            PageResult<Comment> result = PageResult.of(
                    page.getRecords(),
                    page.getTotal(),
                    page.getSize(),
                    page.getCurrent()
            );

            return Result.success(result);

        } catch (Exception e) {
            log.error("获取评论列表失败", e);
            return Result.error("获取评论列表失败");
        }
    }

    /**
     * 获取直播间最新评论
     */
    @GetMapping("/room/{roomCode}/comments/latest")
    public Result<List<Comment>> getLatestComments(
            @PathVariable String roomCode,
            @RequestParam(defaultValue = "10") int limit) {
        try {
            // 检查直播间是否存在
            LiveRoom room = liveRoomService.findByRoomCode(roomCode);
            if (room == null) {
                return Result.notFound("直播间不存在");
            }

            // 获取最新评论
            List<Comment> comments = commentService.getLatestComments(room.getId(), limit);
            return Result.success(comments);

        } catch (Exception e) {
            log.error("获取最新评论失败", e);
            return Result.error("获取最新评论失败");
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
