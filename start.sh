#!/bin/bash

# 直播系统启动脚本

echo "正在启动直播系统..."

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请先安装JDK 8或更高版本"
    exit 1
fi

# 检查Maven环境
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到Maven环境，请先安装Maven"
    exit 1
fi

# 检查MySQL是否运行
if ! pgrep -x "mysqld" > /dev/null; then
    echo "警告: MySQL服务未运行，请先启动MySQL服务"
fi

# 检查Redis是否运行
if ! pgrep -x "redis-server" > /dev/null; then
    echo "警告: Redis服务未运行，请先启动Redis服务"
fi

# 编译项目
echo "正在编译项目..."
mvn clean compile -q

if [ $? -ne 0 ]; then
    echo "错误: 项目编译失败"
    exit 1
fi

# 启动应用
echo "正在启动应用..."
mvn spring-boot:run

echo "直播系统启动完成！"
echo "访问地址: http://localhost:8080"
echo "API文档: http://localhost:8080/api"
echo "数据库监控: http://localhost:8080/druid"
