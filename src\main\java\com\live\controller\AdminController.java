package com.live.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.live.common.PageResult;
import com.live.common.Result;
import com.live.entity.Admin;
import com.live.entity.Anchor;
import com.live.entity.LiveRoom;
import com.live.entity.User;
import com.live.service.*;
import com.live.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 后台管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/admin")
@RequiredArgsConstructor
public class AdminController {

    private final AdminService adminService;
    private final UserService userService;
    private final AnchorService anchorService;
    private final LiveRoomService liveRoomService;
    private final RobotService robotService;
    private final StatisticsService statisticsService;
    private final JwtUtil jwtUtil;

    /**
     * 管理员登录
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> adminLogin(@RequestBody Map<String, String> loginData) {
        try {
            String username = loginData.get("username");
            String password = loginData.get("password");

            if (username == null || username.trim().isEmpty()) {
                return Result.badRequest("用户名不能为空");
            }
            if (password == null || password.trim().isEmpty()) {
                return Result.badRequest("密码不能为空");
            }

            // 验证登录
            Admin admin = adminService.login(username, password);
            if (admin == null) {
                return Result.error("用户名或密码错误");
            }

            // 生成JWT token（这里需要扩展JwtUtil支持管理员token）
            String token = jwtUtil.generateAnchorToken(admin.getId(), admin.getUsername()); // 临时使用anchor token

            // 返回结果（不包含密码）
            admin.setPassword(null);
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            result.put("admin", admin);

            log.info("管理员登录成功：adminId={}, username={}", admin.getId(), admin.getUsername());
            return Result.success("登录成功", result);

        } catch (Exception e) {
            log.error("管理员登录失败", e);
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 获取系统统计概览
     */
    @GetMapping("/overview")
    public Result<Map<String, Object>> getSystemOverview() {
        try {
            Map<String, Object> overview = statisticsService.getSystemOverview();
            return Result.success(overview);
        } catch (Exception e) {
            log.error("获取系统统计概览失败", e);
            return Result.error("获取系统统计概览失败");
        }
    }

    /**
     * 获取用户统计
     */
    @GetMapping("/stats/users")
    public Result<Map<String, Object>> getUserStatistics() {
        try {
            Map<String, Object> stats = statisticsService.getUserStatistics();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取用户统计失败", e);
            return Result.error("获取用户统计失败");
        }
    }

    /**
     * 获取直播统计
     */
    @GetMapping("/stats/live")
    public Result<Map<String, Object>> getLiveStatistics() {
        try {
            Map<String, Object> stats = statisticsService.getLiveStatistics();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取直播统计失败", e);
            return Result.error("获取直播统计失败");
        }
    }

    /**
     * 获取机器人统计
     */
    @GetMapping("/stats/robots")
    public Result<Map<String, Object>> getRobotStatistics() {
        try {
            Map<String, Object> stats = statisticsService.getRobotStatistics();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取机器人统计失败", e);
            return Result.error("获取机器人统计失败");
        }
    }

    /**
     * 分页获取用户列表
     */
    @GetMapping("/users")
    public Result<PageResult<User>> getUserList(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Page<User> page = new Page<>(current, size);
            IPage<User> result = userService.page(page);
            
            PageResult<User> pageResult = PageResult.of(
                    result.getRecords(),
                    result.getTotal(),
                    result.getSize(),
                    result.getCurrent()
            );
            
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("获取用户列表失败", e);
            return Result.error("获取用户列表失败");
        }
    }

    /**
     * 分页获取主播列表
     */
    @GetMapping("/anchors")
    public Result<PageResult<Anchor>> getAnchorList(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Page<Anchor> page = new Page<>(current, size);
            IPage<Anchor> result = anchorService.page(page);
            
            // 清除密码字段
            result.getRecords().forEach(anchor -> anchor.setPassword(null));
            
            PageResult<Anchor> pageResult = PageResult.of(
                    result.getRecords(),
                    result.getTotal(),
                    result.getSize(),
                    result.getCurrent()
            );
            
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("获取主播列表失败", e);
            return Result.error("获取主播列表失败");
        }
    }

    /**
     * 创建主播
     */
    @PostMapping("/anchors")
    public Result<Void> createAnchor(@RequestBody Anchor anchor) {
        try {
            if (anchor.getUsername() == null || anchor.getUsername().trim().isEmpty()) {
                return Result.badRequest("用户名不能为空");
            }
            if (anchor.getPassword() == null || anchor.getPassword().trim().isEmpty()) {
                return Result.badRequest("密码不能为空");
            }
            if (anchor.getAnchorCode() == null || anchor.getAnchorCode().trim().isEmpty()) {
                return Result.badRequest("主播编码不能为空");
            }

            boolean success = anchorService.createAnchor(anchor);
            if (!success) {
                return Result.error("创建主播失败，用户名或主播编码可能已存在");
            }

            return Result.success("创建主播成功");
        } catch (Exception e) {
            log.error("创建主播失败", e);
            return Result.error("创建主播失败");
        }
    }

    /**
     * 更新主播状态
     */
    @PutMapping("/anchors/{anchorId}/status")
    public Result<Void> updateAnchorStatus(
            @PathVariable Long anchorId,
            @RequestBody Map<String, Integer> statusData) {
        try {
            Integer status = statusData.get("status");
            if (status == null) {
                return Result.badRequest("状态不能为空");
            }

            boolean success = anchorService.updateStatus(anchorId, status);
            if (!success) {
                return Result.error("更新主播状态失败，主播不存在");
            }

            return Result.success("更新主播状态成功");
        } catch (Exception e) {
            log.error("更新主播状态失败", e);
            return Result.error("更新主播状态失败");
        }
    }

    /**
     * 重置主播密码
     */
    @PostMapping("/anchors/{anchorId}/reset-password")
    public Result<Void> resetAnchorPassword(
            @PathVariable Long anchorId,
            @RequestBody Map<String, String> passwordData) {
        try {
            String newPassword = passwordData.get("newPassword");
            if (newPassword == null || newPassword.trim().isEmpty()) {
                return Result.badRequest("新密码不能为空");
            }
            if (newPassword.length() < 6) {
                return Result.badRequest("新密码长度不能少于6位");
            }

            boolean success = anchorService.resetPassword(anchorId, newPassword);
            if (!success) {
                return Result.error("重置密码失败，主播不存在");
            }

            return Result.success("重置密码成功");
        } catch (Exception e) {
            log.error("重置主播密码失败", e);
            return Result.error("重置主播密码失败");
        }
    }

    /**
     * 分页获取直播间列表
     */
    @GetMapping("/rooms")
    public Result<PageResult<LiveRoom>> getRoomList(
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Page<LiveRoom> page = new Page<>(current, size);
            IPage<LiveRoom> result = liveRoomService.page(page);
            
            PageResult<LiveRoom> pageResult = PageResult.of(
                    result.getRecords(),
                    result.getTotal(),
                    result.getSize(),
                    result.getCurrent()
            );
            
            return Result.success(pageResult);
        } catch (Exception e) {
            log.error("获取直播间列表失败", e);
            return Result.error("获取直播间列表失败");
        }
    }

    /**
     * 强制结束直播
     */
    @PostMapping("/rooms/{roomId}/force-stop")
    public Result<Void> forceStopLive(@PathVariable Long roomId) {
        try {
            boolean success = liveRoomService.stopLive(roomId, null);
            if (!success) {
                return Result.error("强制结束直播失败");
            }

            return Result.success("强制结束直播成功");
        } catch (Exception e) {
            log.error("强制结束直播失败", e);
            return Result.error("强制结束直播失败");
        }
    }
}
