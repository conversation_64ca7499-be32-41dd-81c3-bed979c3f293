package com.live.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 机器人实体类
 */
@Data
@TableName("robots")
public class Robot {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("robot_code")
    private String robotCode;
    
    @TableField("nickname")
    private String nickname;
    
    @TableField("avatar_url")
    private String avatarUrl;
    
    @TableField("gender")
    private Integer gender;
    
    @TableField("personality")
    private String personality;
    
    @TableField("usage_count")
    private Integer usageCount;
    
    @TableField("usage_frequency")
    private BigDecimal usageFrequency;
    
    @TableField("status")
    private Integer status;
    
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
}
