package com.live.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.live.entity.Admin;
import com.live.mapper.AdminMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 系统管理员服务
 */
@Slf4j
@Service
public class AdminService extends ServiceImpl<AdminMapper, Admin> {

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    /**
     * 根据用户名查找管理员
     */
    public Admin findByUsername(String username) {
        return this.getOne(new LambdaQueryWrapper<Admin>()
                .eq(Admin::getUsername, username));
    }

    /**
     * 管理员登录验证
     */
    public Admin login(String username, String password) {
        Admin admin = findByUsername(username);
        if (admin == null) {
            log.warn("管理员登录失败：用户名不存在 - {}", username);
            return null;
        }

        if (admin.getStatus() != 1) {
            log.warn("管理员登录失败：账号已禁用 - {}", username);
            return null;
        }

        if (!passwordEncoder.matches(password, admin.getPassword())) {
            log.warn("管理员登录失败：密码错误 - {}", username);
            return null;
        }

        // 更新最后登录时间
        admin.setLastLoginTime(LocalDateTime.now());
        this.updateById(admin);

        log.info("管理员登录成功 - {}", username);
        return admin;
    }

    /**
     * 创建管理员
     */
    public boolean createAdmin(Admin admin) {
        // 检查用户名是否已存在
        if (findByUsername(admin.getUsername()) != null) {
            log.warn("创建管理员失败：用户名已存在 - {}", admin.getUsername());
            return false;
        }

        // 加密密码
        admin.setPassword(passwordEncoder.encode(admin.getPassword()));
        admin.setStatus(1); // 默认正常状态

        boolean result = this.save(admin);
        if (result) {
            log.info("创建管理员成功 - {}", admin.getUsername());
        }
        return result;
    }

    /**
     * 更新管理员密码
     */
    public boolean updatePassword(Long adminId, String oldPassword, String newPassword) {
        Admin admin = this.getById(adminId);
        if (admin == null) {
            return false;
        }

        if (!passwordEncoder.matches(oldPassword, admin.getPassword())) {
            log.warn("更新密码失败：原密码错误 - {}", admin.getUsername());
            return false;
        }

        admin.setPassword(passwordEncoder.encode(newPassword));
        boolean result = this.updateById(admin);
        if (result) {
            log.info("管理员密码更新成功 - {}", admin.getUsername());
        }
        return result;
    }

    /**
     * 重置管理员密码
     */
    public boolean resetPassword(Long adminId, String newPassword) {
        Admin admin = this.getById(adminId);
        if (admin == null) {
            return false;
        }

        admin.setPassword(passwordEncoder.encode(newPassword));
        boolean result = this.updateById(admin);
        if (result) {
            log.info("管理员密码重置成功 - {}", admin.getUsername());
        }
        return result;
    }

    /**
     * 更新管理员状态
     */
    public boolean updateStatus(Long adminId, Integer status) {
        Admin admin = this.getById(adminId);
        if (admin == null) {
            return false;
        }

        admin.setStatus(status);
        boolean result = this.updateById(admin);
        if (result) {
            log.info("管理员状态更新成功 - {} -> {}", admin.getUsername(), status);
        }
        return result;
    }

    /**
     * 检查是否为超级管理员
     */
    public boolean isSuperAdmin(Long adminId) {
        Admin admin = this.getById(adminId);
        return admin != null && "super".equals(admin.getRole());
    }

    /**
     * 检查是否有权限操作目标管理员
     */
    public boolean hasPermission(Long operatorId, Long targetId) {
        if (operatorId.equals(targetId)) {
            return true; // 可以操作自己
        }

        Admin operator = this.getById(operatorId);
        if (operator == null) {
            return false;
        }

        // 超级管理员可以操作所有人
        if ("super".equals(operator.getRole())) {
            return true;
        }

        // 普通管理员不能操作其他管理员
        return false;
    }
}
