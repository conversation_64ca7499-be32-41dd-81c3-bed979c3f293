package com.live.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.live.entity.Comment;
import com.live.mapper.CommentMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 评论服务
 */
@Slf4j
@Service
public class CommentService extends ServiceImpl<CommentMapper, Comment> {

    /**
     * 获取直播间评论列表（分页）
     */
    public IPage<Comment> getRoomComments(Long roomId, int current, int size) {
        Page<Comment> page = new Page<>(current, size);
        return this.page(page, new LambdaQueryWrapper<Comment>()
                .eq(Comment::getRoomId, roomId)
                .eq(Comment::getStatus, 1) // 正常状态
                .orderByDesc(Comment::getCreatedTime));
    }

    /**
     * 获取直播间最新评论
     */
    public List<Comment> getLatestComments(Long roomId, int limit) {
        return this.list(new LambdaQueryWrapper<Comment>()
                .eq(Comment::getRoomId, roomId)
                .eq(Comment::getStatus, 1)
                .orderByDesc(Comment::getCreatedTime)
                .last("LIMIT " + limit));
    }

    /**
     * 发送用户评论
     */
    public Comment sendUserComment(Long roomId, Long userId, String content, String ipAddress) {
        Comment comment = new Comment();
        comment.setRoomId(roomId);
        comment.setUserId(userId);
        comment.setContent(content);
        comment.setCommentType(1); // 文本
        comment.setIsRobot(0); // 非机器人
        comment.setIpAddress(ipAddress);
        comment.setStatus(1); // 正常

        this.save(comment);
        log.info("用户发送评论成功：roomId={}, userId={}, content={}", roomId, userId, content);
        return comment;
    }

    /**
     * 发送机器人评论
     */
    public Comment sendRobotComment(Long roomId, Long robotId, String content) {
        Comment comment = new Comment();
        comment.setRoomId(roomId);
        comment.setRobotId(robotId);
        comment.setContent(content);
        comment.setCommentType(1); // 文本
        comment.setIsRobot(1); // 机器人
        comment.setStatus(1); // 正常

        this.save(comment);
        log.info("机器人发送评论成功：roomId={}, robotId={}, content={}", roomId, robotId, content);
        return comment;
    }

    /**
     * 删除评论
     */
    public boolean deleteComment(Long commentId) {
        Comment comment = this.getById(commentId);
        if (comment != null) {
            comment.setStatus(0); // 删除状态
            return this.updateById(comment);
        }
        return false;
    }

    /**
     * 批量导入评论到新直播间
     */
    public int importComments(Long sourceRoomId, Long targetRoomId) {
        // 获取源直播间的所有评论
        List<Comment> sourceComments = this.list(new LambdaQueryWrapper<Comment>()
                .eq(Comment::getRoomId, sourceRoomId)
                .eq(Comment::getStatus, 1)
                .orderByAsc(Comment::getCreatedTime));

        if (sourceComments.isEmpty()) {
            return 0;
        }

        // 复制评论到目标直播间
        int importCount = 0;
        for (Comment sourceComment : sourceComments) {
            Comment newComment = new Comment();
            newComment.setRoomId(targetRoomId);
            newComment.setUserId(sourceComment.getUserId());
            newComment.setRobotId(sourceComment.getRobotId());
            newComment.setContent(sourceComment.getContent());
            newComment.setCommentType(sourceComment.getCommentType());
            newComment.setIsRobot(sourceComment.getIsRobot());
            newComment.setStatus(1);

            if (this.save(newComment)) {
                importCount++;
            }
        }

        log.info("评论导入完成：sourceRoomId={}, targetRoomId={}, importCount={}", 
                sourceRoomId, targetRoomId, importCount);
        return importCount;
    }

    /**
     * 获取直播间评论统计
     */
    public long getRoomCommentCount(Long roomId) {
        return this.count(new LambdaQueryWrapper<Comment>()
                .eq(Comment::getRoomId, roomId)
                .eq(Comment::getStatus, 1));
    }

    /**
     * 获取用户在直播间的评论数
     */
    public long getUserCommentCount(Long roomId, Long userId) {
        return this.count(new LambdaQueryWrapper<Comment>()
                .eq(Comment::getRoomId, roomId)
                .eq(Comment::getUserId, userId)
                .eq(Comment::getIsRobot, 0)
                .eq(Comment::getStatus, 1));
    }
}
