# 直播系统

一个基于Spring Boot的完整直播系统，包含观众端、主播端和机器人互动功能。

## 系统架构

### 技术栈
- **后端**: Spring Boot 2.7.18 + MyBatis Plus + MySQL + Redis
- **直播**: 阿里云直播服务
- **认证**: JWT + 微信网页授权
- **实时通信**: WebSocket
- **前端**: Vue.js 3 (推荐)

### 核心功能

#### 1. 观众端
- 微信授权登录，获取用户完整信息（昵称、头像、手机号等）
- 直播观看功能
- 实时评论互动
- 观看行为记录（进入/离开时间、观看时长等）
- 评论导入功能（可将评论导入到其他直播间）

#### 2. 主播端
- 主播账号密码登录
- 直播间管理（创建、编辑、删除）
- 获取推流/播放地址
- 开播/结束直播控制
- 实时观看数据查看

#### 3. 机器人互动系统
- 机器人信息管理（昵称、头像、性格等）
- 消息模板配置（支持多种触发条件）
- 智能发送策略（定时发送、观众数量触发）
- 使用频率统计和推荐
- 房间级别的机器人配置

#### 4. 后台管理系统
- 管理员登录认证
- 用户数据统计和管理
- 主播账号管理
- 直播间监控和管理
- 机器人系统管理
- 数据统计报表

## 数据库设计

### 核心表结构
- `users` - 用户表（观众）
- `anchors` - 主播表
- `live_rooms` - 直播间表
- `comments` - 评论表
- `robots` - 机器人表
- `robot_messages` - 机器人消息模板表
- `room_robot_configs` - 直播间机器人配置表
- `user_watch_records` - 用户观看记录表
- `admins` - 系统管理员表

详细的数据库结构请参考 `database/live_system.sql`

## 快速开始

### 1. 环境要求
- JDK 8+
- MySQL 8.0+
- Redis 6.0+
- Maven 3.6+

### 2. 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE live_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入数据库结构和初始数据
source database/live_system.sql;
```

### 3. 配置文件
修改 `src/main/resources/application.yml` 中的配置：

```yaml
spring:
  datasource:
    url: ****************************************************************************************************************************************************
    username: your_mysql_username
    password: your_mysql_password
  
  redis:
    host: localhost
    port: 6379
    password: your_redis_password

live:
  wechat:
    app-id: your-wechat-app-id
    app-secret: your-wechat-app-secret
    redirect-uri: http://your-domain.com/api/auth/wechat/callback
  
  aliyun:
    live:
      access-key-id: your-access-key-id
      access-key-secret: your-access-key-secret
      domain-name: your-live-domain.com
      push-auth-key: your-push-auth-key
      play-auth-key: your-play-auth-key
```

### 4. 启动应用
```bash
mvn spring-boot:run
```

应用将在 `http://localhost:8080` 启动

## API 接口

### 认证相关
- `GET /api/auth/wechat/auth-url` - 获取微信授权URL
- `GET /api/auth/wechat/callback` - 微信授权回调
- `POST /api/auth/anchor/login` - 主播登录
- `POST /api/auth/validate` - 验证token

### 观众端
- `GET /api/viewer/rooms/live` - 获取正在直播的房间
- `GET /api/viewer/room/{roomCode}` - 获取直播间信息
- `POST /api/viewer/room/{roomCode}/enter` - 进入直播间
- `POST /api/viewer/room/{roomCode}/comment` - 发送评论
- `GET /api/viewer/room/{roomCode}/comments` - 获取评论列表

### 主播端
- `GET /api/anchor/info` - 获取主播信息
- `GET /api/anchor/rooms` - 获取主播的直播间列表
- `POST /api/anchor/room/create` - 创建直播间
- `POST /api/anchor/room/{roomId}/start` - 开始直播
- `POST /api/anchor/room/{roomId}/stop` - 结束直播

### 机器人管理
- `GET /api/robot/list` - 获取机器人列表
- `POST /api/robot/create` - 创建机器人
- `POST /api/robot/{robotId}/messages` - 添加消息模板
- `POST /api/robot/send` - 手动发送机器人消息

### 后台管理
- `POST /api/admin/login` - 管理员登录
- `GET /api/admin/overview` - 系统统计概览
- `GET /api/admin/users` - 用户列表
- `GET /api/admin/anchors` - 主播列表

## WebSocket 连接

### 连接地址
```
ws://localhost:8080/websocket/live/{roomCode}/{token}
```

### 消息格式
```json
{
  "type": "comment",
  "data": {
    "id": 1,
    "content": "这是一条评论",
    "isRobot": 0,
    "createdTime": "2023-12-01T10:00:00"
  }
}
```

## 部署说明

### 1. 生产环境配置
- 修改数据库连接配置
- 配置Redis集群
- 设置正确的微信应用信息
- 配置阿里云直播域名和密钥

### 2. 负载均衡
- 使用Nginx进行负载均衡
- WebSocket需要配置sticky session
- 静态资源建议使用CDN

### 3. 监控和日志
- 集成Spring Boot Actuator
- 配置日志收集系统
- 设置系统监控告警

## 开发指南

### 代码结构
```
src/main/java/com/live/
├── config/          # 配置类
├── controller/      # 控制器
├── entity/          # 实体类
├── mapper/          # MyBatis Mapper
├── service/         # 业务服务
├── util/            # 工具类
├── websocket/       # WebSocket处理
└── exception/       # 异常处理
```

### 扩展功能
1. **支付系统**: 可集成礼物打赏功能
2. **推荐算法**: 基于用户行为的直播间推荐
3. **数据分析**: 更详细的用户行为分析
4. **多媒体支持**: 支持图片、表情等富文本评论
5. **分布式部署**: 支持多实例部署和数据同步

## 注意事项

1. **安全性**: 
   - 所有API都需要进行权限验证
   - 敏感信息不要在日志中输出
   - 定期更新JWT密钥

2. **性能优化**:
   - 使用Redis缓存热点数据
   - 数据库查询优化和索引设计
   - WebSocket连接数量监控

3. **数据一致性**:
   - 观看人数的实时更新
   - 评论的顺序和时间戳
   - 机器人发送频率控制

## 许可证

MIT License

## 联系方式

如有问题请提交Issue或联系开发团队。
