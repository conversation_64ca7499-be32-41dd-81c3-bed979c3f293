package com.live.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 机器人消息模板实体类
 */
@Data
@TableName("robot_messages")
public class RobotMessage {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("robot_id")
    private Long robotId;
    
    @TableField("message_type")
    private Integer messageType;
    
    @TableField("content")
    private String content;
    
    @TableField("trigger_condition")
    private String triggerCondition;
    
    @TableField("weight")
    private Integer weight;
    
    @TableField("status")
    private Integer status;
    
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
}
