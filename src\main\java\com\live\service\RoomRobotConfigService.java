package com.live.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.live.entity.RoomRobotConfig;
import com.live.mapper.RoomRobotConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 直播间机器人配置服务
 */
@Slf4j
@Service
public class RoomRobotConfigService extends ServiceImpl<RoomRobotConfigMapper, RoomRobotConfig> {

    @Value("${live.robot.default-send-interval-min:30}")
    private Integer defaultSendIntervalMin;

    @Value("${live.robot.default-send-interval-max:120}")
    private Integer defaultSendIntervalMax;

    @Value("${live.robot.default-viewer-threshold:10}")
    private Integer defaultViewerThreshold;

    @Value("${live.robot.default-max-messages-per-hour:20}")
    private Integer defaultMaxMessagesPerHour;

    /**
     * 获取房间的机器人配置
     */
    public List<RoomRobotConfig> getRoomConfigs(Long roomId) {
        return this.list(new LambdaQueryWrapper<RoomRobotConfig>()
                .eq(RoomRobotConfig::getRoomId, roomId));
    }

    /**
     * 获取房间启用的机器人配置
     */
    public List<RoomRobotConfig> getEnabledRoomConfigs(Long roomId) {
        return this.list(new LambdaQueryWrapper<RoomRobotConfig>()
                .eq(RoomRobotConfig::getRoomId, roomId)
                .eq(RoomRobotConfig::getIsEnabled, 1));
    }

    /**
     * 获取特定机器人在房间的配置
     */
    public RoomRobotConfig getRoomRobotConfig(Long roomId, Long robotId) {
        return this.getOne(new LambdaQueryWrapper<RoomRobotConfig>()
                .eq(RoomRobotConfig::getRoomId, roomId)
                .eq(RoomRobotConfig::getRobotId, robotId));
    }

    /**
     * 为房间添加机器人配置
     */
    public RoomRobotConfig addRobotToRoom(Long roomId, Long robotId, Boolean isEnabled,
                                        Integer sendIntervalMin, Integer sendIntervalMax,
                                        Integer viewerThreshold, Integer maxMessagesPerHour) {
        // 检查是否已存在配置
        RoomRobotConfig existingConfig = getRoomRobotConfig(roomId, robotId);
        if (existingConfig != null) {
            log.warn("房间机器人配置已存在：roomId={}, robotId={}", roomId, robotId);
            return existingConfig;
        }

        RoomRobotConfig config = new RoomRobotConfig();
        config.setRoomId(roomId);
        config.setRobotId(robotId);
        config.setIsEnabled(isEnabled != null && isEnabled ? 1 : 0);
        config.setSendIntervalMin(sendIntervalMin != null ? sendIntervalMin : defaultSendIntervalMin);
        config.setSendIntervalMax(sendIntervalMax != null ? sendIntervalMax : defaultSendIntervalMax);
        config.setViewerThreshold(viewerThreshold != null ? viewerThreshold : defaultViewerThreshold);
        config.setMaxMessagesPerHour(maxMessagesPerHour != null ? maxMessagesPerHour : defaultMaxMessagesPerHour);

        this.save(config);
        log.info("添加房间机器人配置成功：roomId={}, robotId={}", roomId, robotId);
        return config;
    }

    /**
     * 更新房间机器人配置
     */
    public boolean updateRoomRobotConfig(Long roomId, Long robotId, Boolean isEnabled,
                                       Integer sendIntervalMin, Integer sendIntervalMax,
                                       Integer viewerThreshold, Integer maxMessagesPerHour) {
        RoomRobotConfig config = getRoomRobotConfig(roomId, robotId);
        if (config == null) {
            log.warn("房间机器人配置不存在：roomId={}, robotId={}", roomId, robotId);
            return false;
        }

        if (isEnabled != null) {
            config.setIsEnabled(isEnabled ? 1 : 0);
        }
        if (sendIntervalMin != null) {
            config.setSendIntervalMin(sendIntervalMin);
        }
        if (sendIntervalMax != null) {
            config.setSendIntervalMax(sendIntervalMax);
        }
        if (viewerThreshold != null) {
            config.setViewerThreshold(viewerThreshold);
        }
        if (maxMessagesPerHour != null) {
            config.setMaxMessagesPerHour(maxMessagesPerHour);
        }

        boolean result = this.updateById(config);
        if (result) {
            log.info("更新房间机器人配置成功：roomId={}, robotId={}", roomId, robotId);
        }
        return result;
    }

    /**
     * 启用/禁用房间机器人
     */
    public boolean toggleRoomRobot(Long roomId, Long robotId, boolean enabled) {
        RoomRobotConfig config = getRoomRobotConfig(roomId, robotId);
        if (config == null) {
            // 如果配置不存在，创建一个新的
            addRobotToRoom(roomId, robotId, enabled, null, null, null, null);
            return true;
        }

        config.setIsEnabled(enabled ? 1 : 0);
        boolean result = this.updateById(config);
        if (result) {
            log.info("{}房间机器人成功：roomId={}, robotId={}", enabled ? "启用" : "禁用", roomId, robotId);
        }
        return result;
    }

    /**
     * 移除房间机器人配置
     */
    public boolean removeRobotFromRoom(Long roomId, Long robotId) {
        RoomRobotConfig config = getRoomRobotConfig(roomId, robotId);
        if (config == null) {
            return true; // 已经不存在，认为删除成功
        }

        boolean result = this.removeById(config.getId());
        if (result) {
            log.info("移除房间机器人配置成功：roomId={}, robotId={}", roomId, robotId);
        }
        return result;
    }

    /**
     * 批量为房间添加机器人
     */
    public int batchAddRobotsToRoom(Long roomId, List<Long> robotIds) {
        int count = 0;
        for (Long robotId : robotIds) {
            try {
                RoomRobotConfig config = addRobotToRoom(roomId, robotId, true, null, null, null, null);
                if (config != null) {
                    count++;
                }
            } catch (Exception e) {
                log.error("批量添加机器人到房间失败：roomId={}, robotId={}", roomId, robotId, e);
            }
        }
        log.info("批量添加机器人到房间完成：roomId={}, 成功添加{}个", roomId, count);
        return count;
    }

    /**
     * 复制机器人配置到其他房间
     */
    public int copyRobotConfigsToRoom(Long sourceRoomId, Long targetRoomId) {
        List<RoomRobotConfig> sourceConfigs = getRoomConfigs(sourceRoomId);
        if (sourceConfigs.isEmpty()) {
            return 0;
        }

        int count = 0;
        for (RoomRobotConfig sourceConfig : sourceConfigs) {
            try {
                // 检查目标房间是否已有此机器人配置
                RoomRobotConfig existingConfig = getRoomRobotConfig(targetRoomId, sourceConfig.getRobotId());
                if (existingConfig != null) {
                    continue; // 跳过已存在的配置
                }

                // 创建新配置
                RoomRobotConfig newConfig = new RoomRobotConfig();
                newConfig.setRoomId(targetRoomId);
                newConfig.setRobotId(sourceConfig.getRobotId());
                newConfig.setIsEnabled(sourceConfig.getIsEnabled());
                newConfig.setSendIntervalMin(sourceConfig.getSendIntervalMin());
                newConfig.setSendIntervalMax(sourceConfig.getSendIntervalMax());
                newConfig.setViewerThreshold(sourceConfig.getViewerThreshold());
                newConfig.setMaxMessagesPerHour(sourceConfig.getMaxMessagesPerHour());

                this.save(newConfig);
                count++;

            } catch (Exception e) {
                log.error("复制机器人配置失败：sourceRoomId={}, targetRoomId={}, robotId={}", 
                         sourceRoomId, targetRoomId, sourceConfig.getRobotId(), e);
            }
        }

        log.info("复制机器人配置完成：sourceRoomId={}, targetRoomId={}, 成功复制{}个", 
                sourceRoomId, targetRoomId, count);
        return count;
    }

    /**
     * 获取机器人在所有房间的配置数量
     */
    public long getRobotConfigCount(Long robotId) {
        return this.count(new LambdaQueryWrapper<RoomRobotConfig>()
                .eq(RoomRobotConfig::getRobotId, robotId));
    }

    /**
     * 获取房间的机器人配置数量
     */
    public long getRoomConfigCount(Long roomId) {
        return this.count(new LambdaQueryWrapper<RoomRobotConfig>()
                .eq(RoomRobotConfig::getRoomId, roomId));
    }
}
