package com.live.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.live.entity.Anchor;
import com.live.mapper.AnchorMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 主播服务
 */
@Slf4j
@Service
public class AnchorService extends ServiceImpl<AnchorMapper, Anchor> {

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    /**
     * 根据用户名查找主播
     */
    public Anchor findByUsername(String username) {
        return this.getOne(new LambdaQueryWrapper<Anchor>()
                .eq(Anchor::getUsername, username));
    }

    /**
     * 根据主播编码查找主播
     */
    public Anchor findByAnchorCode(String anchorCode) {
        return this.getOne(new LambdaQueryWrapper<Anchor>()
                .eq(Anchor::getAnchorCode, anchorCode));
    }

    /**
     * 主播登录验证
     */
    public Anchor login(String username, String password) {
        Anchor anchor = findByUsername(username);
        if (anchor == null) {
            log.warn("主播登录失败：用户名不存在 - {}", username);
            return null;
        }

        if (anchor.getStatus() != 1) {
            log.warn("主播登录失败：账号已禁用 - {}", username);
            return null;
        }

        if (!passwordEncoder.matches(password, anchor.getPassword())) {
            log.warn("主播登录失败：密码错误 - {}", username);
            return null;
        }

        // 更新最后登录时间
        anchor.setLastLoginTime(LocalDateTime.now());
        this.updateById(anchor);

        log.info("主播登录成功 - {}", username);
        return anchor;
    }

    /**
     * 创建主播
     */
    public boolean createAnchor(Anchor anchor) {
        // 检查用户名是否已存在
        if (findByUsername(anchor.getUsername()) != null) {
            log.warn("创建主播失败：用户名已存在 - {}", anchor.getUsername());
            return false;
        }

        // 检查主播编码是否已存在
        if (findByAnchorCode(anchor.getAnchorCode()) != null) {
            log.warn("创建主播失败：主播编码已存在 - {}", anchor.getAnchorCode());
            return false;
        }

        // 加密密码
        anchor.setPassword(passwordEncoder.encode(anchor.getPassword()));
        anchor.setStatus(1); // 默认正常状态

        boolean result = this.save(anchor);
        if (result) {
            log.info("创建主播成功 - {}", anchor.getUsername());
        }
        return result;
    }

    /**
     * 更新主播密码
     */
    public boolean updatePassword(Long anchorId, String oldPassword, String newPassword) {
        Anchor anchor = this.getById(anchorId);
        if (anchor == null) {
            return false;
        }

        if (!passwordEncoder.matches(oldPassword, anchor.getPassword())) {
            log.warn("更新密码失败：原密码错误 - {}", anchor.getUsername());
            return false;
        }

        anchor.setPassword(passwordEncoder.encode(newPassword));
        boolean result = this.updateById(anchor);
        if (result) {
            log.info("主播密码更新成功 - {}", anchor.getUsername());
        }
        return result;
    }

    /**
     * 重置主播密码
     */
    public boolean resetPassword(Long anchorId, String newPassword) {
        Anchor anchor = this.getById(anchorId);
        if (anchor == null) {
            return false;
        }

        anchor.setPassword(passwordEncoder.encode(newPassword));
        boolean result = this.updateById(anchor);
        if (result) {
            log.info("主播密码重置成功 - {}", anchor.getUsername());
        }
        return result;
    }

    /**
     * 更新主播状态
     */
    public boolean updateStatus(Long anchorId, Integer status) {
        Anchor anchor = this.getById(anchorId);
        if (anchor == null) {
            return false;
        }

        anchor.setStatus(status);
        boolean result = this.updateById(anchor);
        if (result) {
            log.info("主播状态更新成功 - {} -> {}", anchor.getUsername(), status);
        }
        return result;
    }
}
