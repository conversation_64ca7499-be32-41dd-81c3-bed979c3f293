package com.live.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.live.entity.User;
import com.live.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 用户服务
 */
@Slf4j
@Service
public class UserService extends ServiceImpl<UserMapper, User> {

    /**
     * 根据openid查找用户
     */
    public User findByOpenid(String openid) {
        return this.getOne(new LambdaQueryWrapper<User>()
                .eq(User::getOpenid, openid));
    }

    /**
     * 根据unionid查找用户
     */
    public User findByUnionid(String unionid) {
        if (unionid == null || unionid.trim().isEmpty()) {
            return null;
        }
        return this.getOne(new LambdaQueryWrapper<User>()
                .eq(User::getUnionid, unionid));
    }

    /**
     * 创建或更新用户
     */
    public User createOrUpdateUser(User user) {
        // 先根据unionid查找（如果有unionid的话）
        User existingUser = null;
        if (user.getUnionid() != null && !user.getUnionid().trim().isEmpty()) {
            existingUser = findByUnionid(user.getUnionid());
        }
        
        // 如果根据unionid没找到，再根据openid查找
        if (existingUser == null) {
            existingUser = findByOpenid(user.getOpenid());
        }

        if (existingUser != null) {
            // 更新现有用户信息
            existingUser.setNickname(user.getNickname());
            existingUser.setAvatarUrl(user.getAvatarUrl());
            existingUser.setGender(user.getGender());
            existingUser.setCity(user.getCity());
            existingUser.setProvince(user.getProvince());
            existingUser.setCountry(user.getCountry());
            existingUser.setLastLoginTime(LocalDateTime.now());
            
            // 如果原来没有unionid，现在有了，就更新
            if ((existingUser.getUnionid() == null || existingUser.getUnionid().trim().isEmpty()) 
                && user.getUnionid() != null && !user.getUnionid().trim().isEmpty()) {
                existingUser.setUnionid(user.getUnionid());
            }
            
            this.updateById(existingUser);
            return existingUser;
        } else {
            // 创建新用户
            user.setLastLoginTime(LocalDateTime.now());
            this.save(user);
            return user;
        }
    }

    /**
     * 更新用户手机号
     */
    public boolean updateUserPhone(Long userId, String phone) {
        User user = this.getById(userId);
        if (user != null) {
            user.setPhone(phone);
            return this.updateById(user);
        }
        return false;
    }

    /**
     * 更新用户最后登录时间
     */
    public void updateLastLoginTime(Long userId) {
        User user = this.getById(userId);
        if (user != null) {
            user.setLastLoginTime(LocalDateTime.now());
            this.updateById(user);
        }
    }
}
