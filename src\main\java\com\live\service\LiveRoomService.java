package com.live.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.live.entity.LiveRoom;
import com.live.mapper.LiveRoomMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 直播间服务
 */
@Slf4j
@Service
public class LiveRoomService extends ServiceImpl<LiveRoomMapper, LiveRoom> {

    /**
     * 根据房间编码查找直播间
     */
    public LiveRoom findByRoomCode(String roomCode) {
        return this.getOne(new LambdaQueryWrapper<LiveRoom>()
                .eq(LiveRoom::getRoomCode, roomCode));
    }

    /**
     * 根据主播ID查找直播间
     */
    public List<LiveRoom> findByAnchorId(Long anchorId) {
        return this.list(new LambdaQueryWrapper<LiveRoom>()
                .eq(LiveRoom::getAnchorId, anchorId)
                .orderByDesc(LiveRoom::getCreatedTime));
    }

    /**
     * 获取正在直播的房间
     */
    public List<LiveRoom> getLiveRooms() {
        return this.list(new LambdaQueryWrapper<LiveRoom>()
                .eq(LiveRoom::getStatus, 1) // 直播中
                .orderByDesc(LiveRoom::getViewerCount));
    }

    /**
     * 创建直播间
     */
    public LiveRoom createRoom(Long anchorId, String title, String description, String coverUrl) {
        LiveRoom room = new LiveRoom();
        room.setRoomCode(generateRoomCode());
        room.setAnchorId(anchorId);
        room.setTitle(title);
        room.setDescription(description);
        room.setCoverUrl(coverUrl);
        room.setStatus(0); // 未开播
        room.setViewerCount(0);
        room.setMaxViewerCount(0);

        this.save(room);
        log.info("创建直播间成功：roomId={}, anchorId={}", room.getId(), anchorId);
        return room;
    }

    /**
     * 开始直播
     */
    public boolean startLive(Long roomId, String streamUrl, String playUrl) {
        LiveRoom room = this.getById(roomId);
        if (room == null) {
            log.warn("开播失败：直播间不存在 - roomId={}", roomId);
            return false;
        }

        if (room.getStatus() == 1) {
            log.warn("开播失败：直播间已在直播中 - roomId={}", roomId);
            return false;
        }

        room.setStatus(1); // 直播中
        room.setStreamUrl(streamUrl);
        room.setPlayUrl(playUrl);
        room.setStartTime(LocalDateTime.now());
        room.setViewerCount(0);

        boolean result = this.updateById(room);
        if (result) {
            log.info("开播成功：roomId={}", roomId);
        }
        return result;
    }

    /**
     * 结束直播
     */
    public boolean stopLive(Long roomId, String recordUrl) {
        LiveRoom room = this.getById(roomId);
        if (room == null) {
            log.warn("结束直播失败：直播间不存在 - roomId={}", roomId);
            return false;
        }

        if (room.getStatus() != 1) {
            log.warn("结束直播失败：直播间未在直播中 - roomId={}", roomId);
            return false;
        }

        room.setStatus(2); // 已结束
        room.setEndTime(LocalDateTime.now());
        room.setRecordUrl(recordUrl);

        boolean result = this.updateById(room);
        if (result) {
            log.info("结束直播成功：roomId={}", roomId);
        }
        return result;
    }

    /**
     * 更新观看人数
     */
    public void updateViewerCount(Long roomId, int count) {
        LiveRoom room = this.getById(roomId);
        if (room != null) {
            room.setViewerCount(count);
            if (count > room.getMaxViewerCount()) {
                room.setMaxViewerCount(count);
            }
            this.updateById(room);
        }
    }

    /**
     * 增加观看人数
     */
    public void incrementViewerCount(Long roomId) {
        LiveRoom room = this.getById(roomId);
        if (room != null) {
            int newCount = room.getViewerCount() + 1;
            updateViewerCount(roomId, newCount);
        }
    }

    /**
     * 减少观看人数
     */
    public void decrementViewerCount(Long roomId) {
        LiveRoom room = this.getById(roomId);
        if (room != null && room.getViewerCount() > 0) {
            int newCount = room.getViewerCount() - 1;
            updateViewerCount(roomId, newCount);
        }
    }

    /**
     * 生成房间编码
     */
    private String generateRoomCode() {
        return "ROOM_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
