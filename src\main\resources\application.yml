server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: live-system
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ****************************************************************************************************************************************************
      username: root
      password: 123456
      # 连接池配置
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 监控配置
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin123

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/*.xml

# 日志配置
logging:
  level:
    com.live: debug
    org.springframework.web: info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# 自定义配置
live:
  # JWT配置
  jwt:
    secret: live-system-jwt-secret-key-2023
    expiration: 86400000  # 24小时
    header: Authorization
    prefix: Bearer 
  
  # 微信配置
  wechat:
    app-id: your-wechat-app-id
    app-secret: your-wechat-app-secret
    redirect-uri: http://your-domain.com/api/auth/wechat/callback
  
  # 阿里云直播配置
  aliyun:
    live:
      access-key-id: your-access-key-id
      access-key-secret: your-access-key-secret
      domain-name: your-live-domain.com
      app-name: live
      region-id: cn-hangzhou
      push-auth-key: your-push-auth-key
      play-auth-key: your-play-auth-key
  
  # 机器人配置
  robot:
    # 默认发送间隔(秒)
    default-send-interval-min: 30
    default-send-interval-max: 120
    # 默认观众数量阈值
    default-viewer-threshold: 10
    # 默认每小时最大消息数
    default-max-messages-per-hour: 20
