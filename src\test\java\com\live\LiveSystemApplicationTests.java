package com.live;

import com.live.entity.Anchor;
import com.live.entity.Robot;
import com.live.service.AnchorService;
import com.live.service.RobotService;
import com.live.util.JwtUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class LiveSystemApplicationTests {

    @Autowired
    private AnchorService anchorService;

    @Autowired
    private RobotService robotService;

    @Autowired
    private JwtUtil jwtUtil;

    @Test
    void contextLoads() {
        // 测试Spring上下文是否正常加载
        assertNotNull(anchorService);
        assertNotNull(robotService);
        assertNotNull(jwtUtil);
    }

    @Test
    void testAnchorService() {
        // 测试主播服务
        Anchor anchor = new Anchor();
        anchor.setAnchorCode("TEST_ANCHOR_001");
        anchor.setUsername("testanchor");
        anchor.setPassword("123456");
        anchor.setRealName("测试主播");
        anchor.setNickname("测试主播昵称");

        // 创建主播
        boolean created = anchorService.createAnchor(anchor);
        assertTrue(created, "主播创建应该成功");

        // 查找主播
        Anchor foundAnchor = anchorService.findByUsername("testanchor");
        assertNotNull(foundAnchor, "应该能找到创建的主播");
        assertEquals("测试主播", foundAnchor.getRealName());

        // 测试登录
        Anchor loginAnchor = anchorService.login("testanchor", "123456");
        assertNotNull(loginAnchor, "主播登录应该成功");

        // 清理测试数据
        anchorService.removeById(foundAnchor.getId());
    }

    @Test
    void testRobotService() {
        // 测试机器人服务
        Robot robot = robotService.createRobot("测试机器人", "http://example.com/avatar.jpg", 1, "活泼开朗");
        assertNotNull(robot, "机器人创建应该成功");

        // 查找机器人
        Robot foundRobot = robotService.findByRobotCode(robot.getRobotCode());
        assertNotNull(foundRobot, "应该能找到创建的机器人");
        assertEquals("测试机器人", foundRobot.getNickname());

        // 更新机器人
        boolean updated = robotService.updateRobot(robot.getId(), "更新后的机器人", null, 2, "温柔体贴");
        assertTrue(updated, "机器人更新应该成功");

        // 清理测试数据
        robotService.removeById(robot.getId());
    }

    @Test
    void testJwtUtil() {
        // 测试JWT工具类
        String token = jwtUtil.generateUserToken(123L, "test_openid");
        assertNotNull(token, "应该能生成用户token");

        // 验证token
        boolean valid = jwtUtil.validateToken(token);
        assertTrue(valid, "生成的token应该是有效的");

        // 从token中获取用户ID
        Long userId = jwtUtil.getUserIdFromToken(token);
        assertEquals(123L, userId, "应该能从token中正确获取用户ID");

        // 测试主播token
        String anchorToken = jwtUtil.generateAnchorToken(456L, "test_anchor");
        assertNotNull(anchorToken, "应该能生成主播token");

        Long anchorId = jwtUtil.getAnchorIdFromToken(anchorToken);
        assertEquals(456L, anchorId, "应该能从token中正确获取主播ID");
    }
}
