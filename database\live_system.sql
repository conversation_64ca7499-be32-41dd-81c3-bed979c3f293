-- 直播系统数据库设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS live_system DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE live_system;

-- 1. 用户表 (观众)
CREATE TABLE `users` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    `openid` VARCHAR(64) NOT NULL UNIQUE COMMENT '微信openid',
    `unionid` VARCHAR(64) COMMENT '微信unionid',
    `nickname` VARCHAR(100) COMMENT '用户昵称',
    `avatar_url` VARCHAR(500) COMMENT '头像URL',
    `phone` VARCHAR(20) COMMENT '手机号',
    `gender` TINYINT DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    `city` VARCHAR(50) COMMENT '城市',
    `province` VARCHAR(50) COMMENT '省份',
    `country` VARCHAR(50) COMMENT '国家',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    `last_login_time` DATETIME COMMENT '最后登录时间',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_openid` (`openid`),
    INDEX `idx_unionid` (`unionid`),
    INDEX `idx_phone` (`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 2. 主播表
CREATE TABLE `anchors` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主播ID',
    `anchor_code` VARCHAR(32) NOT NULL UNIQUE COMMENT '主播编码',
    `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '登录用户名',
    `password` VARCHAR(128) NOT NULL COMMENT '登录密码(加密)',
    `real_name` VARCHAR(50) COMMENT '真实姓名',
    `nickname` VARCHAR(100) COMMENT '主播昵称',
    `avatar_url` VARCHAR(500) COMMENT '头像URL',
    `phone` VARCHAR(20) COMMENT '手机号',
    `email` VARCHAR(100) COMMENT '邮箱',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    `last_login_time` DATETIME COMMENT '最后登录时间',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_anchor_code` (`anchor_code`),
    INDEX `idx_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='主播表';

-- 3. 直播间表
CREATE TABLE `live_rooms` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '直播间ID',
    `room_code` VARCHAR(32) NOT NULL UNIQUE COMMENT '直播间编码',
    `anchor_id` BIGINT NOT NULL COMMENT '主播ID',
    `title` VARCHAR(200) NOT NULL COMMENT '直播间标题',
    `description` TEXT COMMENT '直播间描述',
    `cover_url` VARCHAR(500) COMMENT '封面图URL',
    `stream_url` VARCHAR(500) COMMENT '推流地址',
    `play_url` VARCHAR(500) COMMENT '播放地址',
    `record_url` VARCHAR(500) COMMENT '录播地址',
    `status` TINYINT DEFAULT 0 COMMENT '状态：0-未开播，1-直播中，2-已结束',
    `viewer_count` INT DEFAULT 0 COMMENT '观看人数',
    `max_viewer_count` INT DEFAULT 0 COMMENT '最大观看人数',
    `start_time` DATETIME COMMENT '开播时间',
    `end_time` DATETIME COMMENT '结束时间',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_room_code` (`room_code`),
    INDEX `idx_anchor_id` (`anchor_id`),
    INDEX `idx_status` (`status`),
    FOREIGN KEY (`anchor_id`) REFERENCES `anchors`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间表';

-- 4. 机器人表
CREATE TABLE `robots` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '机器人ID',
    `robot_code` VARCHAR(32) NOT NULL UNIQUE COMMENT '机器人编码',
    `nickname` VARCHAR(100) NOT NULL COMMENT '机器人昵称',
    `avatar_url` VARCHAR(500) COMMENT '头像URL',
    `gender` TINYINT DEFAULT 1 COMMENT '性别：1-男，2-女',
    `personality` VARCHAR(200) COMMENT '性格描述',
    `usage_count` INT DEFAULT 0 COMMENT '使用次数',
    `usage_frequency` DECIMAL(5,2) DEFAULT 0.00 COMMENT '使用频率',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_robot_code` (`robot_code`),
    INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器人表';

-- 5. 机器人消息模板表
CREATE TABLE `robot_messages` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '消息ID',
    `robot_id` BIGINT NOT NULL COMMENT '机器人ID',
    `message_type` TINYINT DEFAULT 1 COMMENT '消息类型：1-文本，2-表情，3-图片',
    `content` TEXT NOT NULL COMMENT '消息内容',
    `trigger_condition` VARCHAR(100) COMMENT '触发条件',
    `weight` INT DEFAULT 1 COMMENT '权重(影响发送概率)',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_robot_id` (`robot_id`),
    INDEX `idx_message_type` (`message_type`),
    FOREIGN KEY (`robot_id`) REFERENCES `robots`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器人消息模板表';

-- 6. 直播间机器人配置表
CREATE TABLE `room_robot_configs` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    `room_id` BIGINT NOT NULL COMMENT '直播间ID',
    `robot_id` BIGINT NOT NULL COMMENT '机器人ID',
    `is_enabled` TINYINT DEFAULT 1 COMMENT '是否启用：0-否，1-是',
    `send_interval_min` INT DEFAULT 30 COMMENT '发送间隔最小值(秒)',
    `send_interval_max` INT DEFAULT 120 COMMENT '发送间隔最大值(秒)',
    `viewer_threshold` INT DEFAULT 10 COMMENT '观众数量阈值',
    `max_messages_per_hour` INT DEFAULT 20 COMMENT '每小时最大消息数',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY `uk_room_robot` (`room_id`, `robot_id`),
    INDEX `idx_room_id` (`room_id`),
    INDEX `idx_robot_id` (`robot_id`),
    FOREIGN KEY (`room_id`) REFERENCES `live_rooms`(`id`),
    FOREIGN KEY (`robot_id`) REFERENCES `robots`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间机器人配置表';

-- 7. 用户观看记录表
CREATE TABLE `user_watch_records` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '记录ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `room_id` BIGINT NOT NULL COMMENT '直播间ID',
    `enter_time` DATETIME NOT NULL COMMENT '进入时间',
    `leave_time` DATETIME COMMENT '离开时间',
    `watch_duration` INT DEFAULT 0 COMMENT '观看时长(秒)',
    `ip_address` VARCHAR(50) COMMENT 'IP地址',
    `user_agent` VARCHAR(500) COMMENT '用户代理',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_room_id` (`room_id`),
    INDEX `idx_enter_time` (`enter_time`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`),
    FOREIGN KEY (`room_id`) REFERENCES `live_rooms`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户观看记录表';

-- 8. 评论表
CREATE TABLE `comments` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '评论ID',
    `room_id` BIGINT NOT NULL COMMENT '直播间ID',
    `user_id` BIGINT COMMENT '用户ID(真实用户)',
    `robot_id` BIGINT COMMENT '机器人ID(机器人评论)',
    `content` TEXT NOT NULL COMMENT '评论内容',
    `comment_type` TINYINT DEFAULT 1 COMMENT '评论类型：1-文本，2-表情，3-图片',
    `is_robot` TINYINT DEFAULT 0 COMMENT '是否机器人：0-否，1-是',
    `ip_address` VARCHAR(50) COMMENT 'IP地址',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-删除，1-正常，2-审核中',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX `idx_room_id` (`room_id`),
    INDEX `idx_user_id` (`user_id`),
    INDEX `idx_robot_id` (`robot_id`),
    INDEX `idx_created_time` (`created_time`),
    INDEX `idx_is_robot` (`is_robot`),
    FOREIGN KEY (`room_id`) REFERENCES `live_rooms`(`id`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`),
    FOREIGN KEY (`robot_id`) REFERENCES `robots`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

-- 9. 评论导入记录表
CREATE TABLE `comment_import_records` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '导入记录ID',
    `source_room_id` BIGINT NOT NULL COMMENT '源直播间ID',
    `target_room_id` BIGINT NOT NULL COMMENT '目标直播间ID',
    `import_count` INT DEFAULT 0 COMMENT '导入数量',
    `import_status` TINYINT DEFAULT 0 COMMENT '导入状态：0-进行中，1-成功，2-失败',
    `error_message` TEXT COMMENT '错误信息',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `completed_time` DATETIME COMMENT '完成时间',
    INDEX `idx_source_room` (`source_room_id`),
    INDEX `idx_target_room` (`target_room_id`),
    INDEX `idx_import_status` (`import_status`),
    FOREIGN KEY (`source_room_id`) REFERENCES `live_rooms`(`id`),
    FOREIGN KEY (`target_room_id`) REFERENCES `live_rooms`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论导入记录表';

-- 10. 系统管理员表
CREATE TABLE `admins` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '管理员ID',
    `username` VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    `password` VARCHAR(128) NOT NULL COMMENT '密码(加密)',
    `real_name` VARCHAR(50) COMMENT '真实姓名',
    `email` VARCHAR(100) COMMENT '邮箱',
    `phone` VARCHAR(20) COMMENT '手机号',
    `role` VARCHAR(20) DEFAULT 'admin' COMMENT '角色：admin-管理员，super-超级管理员',
    `status` TINYINT DEFAULT 1 COMMENT '状态：0-禁用，1-正常',
    `last_login_time` DATETIME COMMENT '最后登录时间',
    `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX `idx_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统管理员表';

-- 11. 机器人发送日志表
CREATE TABLE `robot_send_logs` (
    `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    `room_id` BIGINT NOT NULL COMMENT '直播间ID',
    `robot_id` BIGINT NOT NULL COMMENT '机器人ID',
    `message_id` BIGINT NOT NULL COMMENT '消息模板ID',
    `content` TEXT NOT NULL COMMENT '发送内容',
    `send_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    `trigger_type` VARCHAR(50) COMMENT '触发类型：timer-定时，viewer_count-观众数量',
    `viewer_count_at_send` INT COMMENT '发送时观众数量',
    INDEX `idx_room_id` (`room_id`),
    INDEX `idx_robot_id` (`robot_id`),
    INDEX `idx_send_time` (`send_time`),
    FOREIGN KEY (`room_id`) REFERENCES `live_rooms`(`id`),
    FOREIGN KEY (`robot_id`) REFERENCES `robots`(`id`),
    FOREIGN KEY (`message_id`) REFERENCES `robot_messages`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='机器人发送日志表';

-- 插入初始数据

-- 插入默认管理员
INSERT INTO `admins` (`username`, `password`, `real_name`, `role`) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLO.TAjqKWO6', '系统管理员', 'super');
-- 默认密码: admin123

-- 插入示例机器人
INSERT INTO `robots` (`robot_code`, `nickname`, `avatar_url`, `gender`, `personality`) VALUES
('ROBOT_001', '小美', 'https://example.com/avatar1.jpg', 2, '活泼开朗，喜欢互动'),
('ROBOT_002', '小帅', 'https://example.com/avatar2.jpg', 1, '幽默风趣，善于调节气氛'),
('ROBOT_003', '小可爱', 'https://example.com/avatar3.jpg', 2, '温柔体贴，善于倾听'),
('ROBOT_004', '小酷', 'https://example.com/avatar4.jpg', 1, '冷静理性，知识渊博'),
('ROBOT_005', '小甜心', 'https://example.com/avatar5.jpg', 2, '甜美可人，充满正能量');

-- 插入机器人消息模板
INSERT INTO `robot_messages` (`robot_id`, `message_type`, `content`, `trigger_condition`, `weight`) VALUES
(1, 1, '哇，主播好棒！', 'viewer_count_increase', 3),
(1, 1, '这个直播太有趣了！', 'timer', 2),
(1, 1, '大家一起来互动吧~', 'timer', 2),
(2, 1, '主播666！', 'viewer_count_increase', 3),
(2, 1, '这波操作可以的', 'timer', 2),
(2, 1, '兄弟们，点个关注不迷路', 'timer', 1),
(3, 1, '主播辛苦了~', 'timer', 2),
(3, 1, '好喜欢这个直播间', 'viewer_count_increase', 2),
(3, 1, '大家都好热情呀', 'timer', 1),
(4, 1, '内容很有深度', 'timer', 2),
(4, 1, '学到了很多东西', 'timer', 2),
(4, 1, '主播专业水平很高', 'viewer_count_increase', 1),
(5, 1, '好开心能看到这个直播', 'timer', 2),
(5, 1, '主播加油哦！', 'viewer_count_increase', 3),
(5, 1, '今天心情特别好', 'timer', 1);
