package com.live.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 直播间机器人配置实体类
 */
@Data
@TableName("room_robot_configs")
public class RoomRobotConfig {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("room_id")
    private Long roomId;
    
    @TableField("robot_id")
    private Long robotId;
    
    @TableField("is_enabled")
    private Integer isEnabled;
    
    @TableField("send_interval_min")
    private Integer sendIntervalMin;
    
    @TableField("send_interval_max")
    private Integer sendIntervalMax;
    
    @TableField("viewer_threshold")
    private Integer viewerThreshold;
    
    @TableField("max_messages_per_hour")
    private Integer maxMessagesPerHour;
    
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
    
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;
}
