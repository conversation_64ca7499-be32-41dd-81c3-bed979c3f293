package com.live.controller;

import com.live.common.Result;
import com.live.entity.Robot;
import com.live.entity.RoomRobotConfig;
import com.live.service.RobotService;
import com.live.service.RoomRobotConfigService;
import com.live.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 房间机器人配置控制器
 */
@Slf4j
@RestController
@RequestMapping("/room/{roomId}/robot")
@RequiredArgsConstructor
public class RoomRobotController {

    private final RoomRobotConfigService roomRobotConfigService;
    private final RobotService robotService;
    private final JwtUtil jwtUtil;

    /**
     * 获取房间的机器人配置列表
     */
    @GetMapping("/configs")
    public Result<List<Map<String, Object>>> getRoomRobotConfigs(@PathVariable Long roomId) {
        try {
            List<RoomRobotConfig> configs = roomRobotConfigService.getRoomConfigs(roomId);
            
            // 获取机器人详细信息
            List<Map<String, Object>> result = configs.stream().map(config -> {
                Robot robot = robotService.getById(config.getRobotId());
                Map<String, Object> item = new HashMap<>();
                item.put("config", config);
                item.put("robot", robot);
                return item;
            }).collect(Collectors.toList());

            return Result.success(result);
        } catch (Exception e) {
            log.error("获取房间机器人配置失败", e);
            return Result.error("获取房间机器人配置失败");
        }
    }

    /**
     * 获取房间启用的机器人配置
     */
    @GetMapping("/configs/enabled")
    public Result<List<Map<String, Object>>> getEnabledRoomRobotConfigs(@PathVariable Long roomId) {
        try {
            List<RoomRobotConfig> configs = roomRobotConfigService.getEnabledRoomConfigs(roomId);
            
            List<Map<String, Object>> result = configs.stream().map(config -> {
                Robot robot = robotService.getById(config.getRobotId());
                Map<String, Object> item = new HashMap<>();
                item.put("config", config);
                item.put("robot", robot);
                return item;
            }).collect(Collectors.toList());

            return Result.success(result);
        } catch (Exception e) {
            log.error("获取房间启用的机器人配置失败", e);
            return Result.error("获取房间启用的机器人配置失败");
        }
    }

    /**
     * 为房间添加机器人
     */
    @PostMapping("/{robotId}")
    public Result<RoomRobotConfig> addRobotToRoom(
            @PathVariable Long roomId,
            @PathVariable Long robotId,
            @RequestBody(required = false) Map<String, Object> configData) {
        try {
            Boolean isEnabled = configData != null ? (Boolean) configData.get("isEnabled") : true;
            Integer sendIntervalMin = configData != null ? (Integer) configData.get("sendIntervalMin") : null;
            Integer sendIntervalMax = configData != null ? (Integer) configData.get("sendIntervalMax") : null;
            Integer viewerThreshold = configData != null ? (Integer) configData.get("viewerThreshold") : null;
            Integer maxMessagesPerHour = configData != null ? (Integer) configData.get("maxMessagesPerHour") : null;

            RoomRobotConfig config = roomRobotConfigService.addRobotToRoom(
                    roomId, robotId, isEnabled, sendIntervalMin, sendIntervalMax, 
                    viewerThreshold, maxMessagesPerHour);

            return Result.success("添加机器人到房间成功", config);
        } catch (Exception e) {
            log.error("添加机器人到房间失败", e);
            return Result.error("添加机器人到房间失败");
        }
    }

    /**
     * 更新房间机器人配置
     */
    @PutMapping("/{robotId}")
    public Result<Void> updateRoomRobotConfig(
            @PathVariable Long roomId,
            @PathVariable Long robotId,
            @RequestBody Map<String, Object> configData) {
        try {
            Boolean isEnabled = (Boolean) configData.get("isEnabled");
            Integer sendIntervalMin = (Integer) configData.get("sendIntervalMin");
            Integer sendIntervalMax = (Integer) configData.get("sendIntervalMax");
            Integer viewerThreshold = (Integer) configData.get("viewerThreshold");
            Integer maxMessagesPerHour = (Integer) configData.get("maxMessagesPerHour");

            boolean success = roomRobotConfigService.updateRoomRobotConfig(
                    roomId, robotId, isEnabled, sendIntervalMin, sendIntervalMax, 
                    viewerThreshold, maxMessagesPerHour);

            if (!success) {
                return Result.error("更新房间机器人配置失败，配置不存在");
            }

            return Result.success("更新房间机器人配置成功");
        } catch (Exception e) {
            log.error("更新房间机器人配置失败", e);
            return Result.error("更新房间机器人配置失败");
        }
    }

    /**
     * 启用/禁用房间机器人
     */
    @PostMapping("/{robotId}/toggle")
    public Result<Void> toggleRoomRobot(
            @PathVariable Long roomId,
            @PathVariable Long robotId,
            @RequestBody Map<String, Boolean> toggleData) {
        try {
            Boolean enabled = toggleData.get("enabled");
            if (enabled == null) {
                return Result.badRequest("enabled参数不能为空");
            }

            boolean success = roomRobotConfigService.toggleRoomRobot(roomId, robotId, enabled);
            if (!success) {
                return Result.error("操作失败");
            }

            return Result.success(enabled ? "启用机器人成功" : "禁用机器人成功");
        } catch (Exception e) {
            log.error("切换房间机器人状态失败", e);
            return Result.error("切换房间机器人状态失败");
        }
    }

    /**
     * 从房间移除机器人
     */
    @DeleteMapping("/{robotId}")
    public Result<Void> removeRobotFromRoom(
            @PathVariable Long roomId,
            @PathVariable Long robotId) {
        try {
            boolean success = roomRobotConfigService.removeRobotFromRoom(roomId, robotId);
            if (!success) {
                return Result.error("移除机器人失败");
            }

            return Result.success("移除机器人成功");
        } catch (Exception e) {
            log.error("从房间移除机器人失败", e);
            return Result.error("从房间移除机器人失败");
        }
    }

    /**
     * 批量为房间添加机器人
     */
    @PostMapping("/batch")
    public Result<Map<String, Object>> batchAddRobotsToRoom(
            @PathVariable Long roomId,
            @RequestBody Map<String, Object> batchData) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> robotIds = (List<Long>) batchData.get("robotIds");

            if (robotIds == null || robotIds.isEmpty()) {
                return Result.badRequest("机器人ID列表不能为空");
            }

            int count = roomRobotConfigService.batchAddRobotsToRoom(roomId, robotIds);

            Map<String, Object> result = new HashMap<>();
            result.put("count", count);

            return Result.success("批量添加机器人成功", result);
        } catch (Exception e) {
            log.error("批量添加机器人到房间失败", e);
            return Result.error("批量添加机器人到房间失败");
        }
    }

    /**
     * 复制机器人配置到其他房间
     */
    @PostMapping("/copy")
    public Result<Map<String, Object>> copyRobotConfigsToRoom(
            @PathVariable Long roomId,
            @RequestBody Map<String, Long> copyData) {
        try {
            Long targetRoomId = copyData.get("targetRoomId");
            if (targetRoomId == null) {
                return Result.badRequest("目标房间ID不能为空");
            }

            int count = roomRobotConfigService.copyRobotConfigsToRoom(roomId, targetRoomId);

            Map<String, Object> result = new HashMap<>();
            result.put("count", count);

            return Result.success("复制机器人配置成功", result);
        } catch (Exception e) {
            log.error("复制机器人配置失败", e);
            return Result.error("复制机器人配置失败");
        }
    }

    /**
     * 获取房间可添加的机器人列表（排除已添加的）
     */
    @GetMapping("/available")
    public Result<List<Robot>> getAvailableRobots(@PathVariable Long roomId) {
        try {
            // 获取所有可用机器人
            List<Robot> allRobots = robotService.getAvailableRobots();
            
            // 获取房间已配置的机器人ID
            List<RoomRobotConfig> roomConfigs = roomRobotConfigService.getRoomConfigs(roomId);
            List<Long> configuredRobotIds = roomConfigs.stream()
                    .map(RoomRobotConfig::getRobotId)
                    .collect(Collectors.toList());

            // 过滤出未配置的机器人
            List<Robot> availableRobots = allRobots.stream()
                    .filter(robot -> !configuredRobotIds.contains(robot.getId()))
                    .collect(Collectors.toList());

            return Result.success(availableRobots);
        } catch (Exception e) {
            log.error("获取房间可添加的机器人列表失败", e);
            return Result.error("获取房间可添加的机器人列表失败");
        }
    }

    /**
     * 获取房间机器人配置统计
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getRoomRobotStats(@PathVariable Long roomId) {
        try {
            long totalCount = roomRobotConfigService.getRoomConfigCount(roomId);
            long enabledCount = roomRobotConfigService.getEnabledRoomConfigs(roomId).size();

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalCount", totalCount);
            stats.put("enabledCount", enabledCount);
            stats.put("disabledCount", totalCount - enabledCount);

            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取房间机器人配置统计失败", e);
            return Result.error("获取房间机器人配置统计失败");
        }
    }
}
