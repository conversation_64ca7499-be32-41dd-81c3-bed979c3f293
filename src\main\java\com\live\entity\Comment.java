package com.live.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 评论实体类
 */
@Data
@TableName("comments")
public class Comment {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("room_id")
    private Long roomId;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("robot_id")
    private Long robotId;
    
    @TableField("content")
    private String content;
    
    @TableField("comment_type")
    private Integer commentType;
    
    @TableField("is_robot")
    private Integer isRobot;
    
    @TableField("ip_address")
    private String ipAddress;
    
    @TableField("status")
    private Integer status;
    
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
}
