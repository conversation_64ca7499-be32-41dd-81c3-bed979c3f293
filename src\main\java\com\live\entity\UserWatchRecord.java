package com.live.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 用户观看记录实体类
 */
@Data
@TableName("user_watch_records")
public class UserWatchRecord {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @TableField("user_id")
    private Long userId;
    
    @TableField("room_id")
    private Long roomId;
    
    @TableField("enter_time")
    private LocalDateTime enterTime;
    
    @TableField("leave_time")
    private LocalDateTime leaveTime;
    
    @TableField("watch_duration")
    private Integer watchDuration;
    
    @TableField("ip_address")
    private String ipAddress;
    
    @TableField("user_agent")
    private String userAgent;
    
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;
}
