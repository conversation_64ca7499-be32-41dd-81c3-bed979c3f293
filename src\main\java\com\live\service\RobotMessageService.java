package com.live.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.live.entity.RobotMessage;
import com.live.mapper.RobotMessageMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Random;

/**
 * 机器人消息模板服务
 */
@Slf4j
@Service
public class RobotMessageService extends ServiceImpl<RobotMessageMapper, RobotMessage> {

    private final Random random = new Random();

    /**
     * 根据机器人ID获取消息模板
     */
    public List<RobotMessage> getMessagesByRobotId(Long robotId) {
        return this.list(new LambdaQueryWrapper<RobotMessage>()
                .eq(RobotMessage::getRobotId, robotId)
                .eq(RobotMessage::getStatus, 1)
                .orderByDesc(RobotMessage::getWeight));
    }

    /**
     * 根据触发条件获取消息模板
     */
    public List<RobotMessage> getMessagesByCondition(Long robotId, String triggerCondition) {
        return this.list(new LambdaQueryWrapper<RobotMessage>()
                .eq(RobotMessage::getRobotId, robotId)
                .eq(RobotMessage::getTriggerCondition, triggerCondition)
                .eq(RobotMessage::getStatus, 1)
                .orderByDesc(RobotMessage::getWeight));
    }

    /**
     * 随机选择一条消息（基于权重）
     */
    public RobotMessage selectRandomMessage(List<RobotMessage> messages) {
        if (messages == null || messages.isEmpty()) {
            return null;
        }

        // 计算总权重
        int totalWeight = messages.stream().mapToInt(RobotMessage::getWeight).sum();
        if (totalWeight <= 0) {
            // 如果没有权重，随机选择
            return messages.get(random.nextInt(messages.size()));
        }

        // 基于权重随机选择
        int randomWeight = random.nextInt(totalWeight);
        int currentWeight = 0;
        
        for (RobotMessage message : messages) {
            currentWeight += message.getWeight();
            if (randomWeight < currentWeight) {
                return message;
            }
        }

        // 兜底返回第一条
        return messages.get(0);
    }

    /**
     * 根据机器人ID和触发条件随机选择消息
     */
    public RobotMessage selectRandomMessageByCondition(Long robotId, String triggerCondition) {
        List<RobotMessage> messages = getMessagesByCondition(robotId, triggerCondition);
        return selectRandomMessage(messages);
    }

    /**
     * 创建消息模板
     */
    public RobotMessage createMessage(Long robotId, Integer messageType, String content, 
                                    String triggerCondition, Integer weight) {
        RobotMessage message = new RobotMessage();
        message.setRobotId(robotId);
        message.setMessageType(messageType);
        message.setContent(content);
        message.setTriggerCondition(triggerCondition);
        message.setWeight(weight != null ? weight : 1);
        message.setStatus(1);

        this.save(message);
        log.info("创建机器人消息模板成功：robotId={}, messageId={}", robotId, message.getId());
        return message;
    }

    /**
     * 更新消息模板
     */
    public boolean updateMessage(Long messageId, Integer messageType, String content, 
                               String triggerCondition, Integer weight) {
        RobotMessage message = this.getById(messageId);
        if (message == null) {
            return false;
        }

        if (messageType != null) {
            message.setMessageType(messageType);
        }
        if (content != null) {
            message.setContent(content);
        }
        if (triggerCondition != null) {
            message.setTriggerCondition(triggerCondition);
        }
        if (weight != null) {
            message.setWeight(weight);
        }

        boolean result = this.updateById(message);
        if (result) {
            log.info("更新机器人消息模板成功：messageId={}", messageId);
        }
        return result;
    }

    /**
     * 删除消息模板
     */
    public boolean deleteMessage(Long messageId) {
        RobotMessage message = this.getById(messageId);
        if (message == null) {
            return false;
        }

        // 软删除：设置状态为禁用
        message.setStatus(0);
        boolean result = this.updateById(message);
        if (result) {
            log.info("删除机器人消息模板成功：messageId={}", messageId);
        }
        return result;
    }

    /**
     * 批量创建消息模板
     */
    public int batchCreateMessages(Long robotId, List<String> contents, String triggerCondition) {
        int count = 0;
        for (String content : contents) {
            if (content != null && !content.trim().isEmpty()) {
                createMessage(robotId, 1, content.trim(), triggerCondition, 1);
                count++;
            }
        }
        log.info("批量创建机器人消息模板完成：robotId={}, count={}", robotId, count);
        return count;
    }

    /**
     * 获取机器人的所有触发条件
     */
    public List<String> getRobotTriggerConditions(Long robotId) {
        return this.baseMapper.selectObjs(new LambdaQueryWrapper<RobotMessage>()
                .select(RobotMessage::getTriggerCondition)
                .eq(RobotMessage::getRobotId, robotId)
                .eq(RobotMessage::getStatus, 1)
                .groupBy(RobotMessage::getTriggerCondition))
                .stream()
                .map(Object::toString)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取消息模板统计信息
     */
    public long getMessageCount(Long robotId) {
        return this.count(new LambdaQueryWrapper<RobotMessage>()
                .eq(RobotMessage::getRobotId, robotId)
                .eq(RobotMessage::getStatus, 1));
    }
}
