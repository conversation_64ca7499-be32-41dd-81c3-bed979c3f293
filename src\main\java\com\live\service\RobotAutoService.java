package com.live.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.live.entity.*;
import com.live.websocket.LiveWebSocketServer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 机器人自动服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RobotAutoService {

    private final RobotService robotService;
    private final RobotMessageService robotMessageService;
    private final LiveRoomService liveRoomService;
    private final CommentService commentService;
    private final RoomRobotConfigService roomRobotConfigService;
    private final RedisTemplate<String, Object> redisTemplate;
    
    private final Random random = new Random();

    /**
     * 定时任务：为所有直播中的房间发送机器人消息
     */
    @Scheduled(fixedDelay = 30000) // 每30秒执行一次
    public void autoSendRobotMessages() {
        try {
            // 获取所有正在直播的房间
            List<LiveRoom> liveRooms = liveRoomService.getLiveRooms();
            
            for (LiveRoom room : liveRooms) {
                processRoomRobotMessages(room);
            }
            
        } catch (Exception e) {
            log.error("自动发送机器人消息任务执行失败", e);
        }
    }

    /**
     * 处理单个房间的机器人消息
     */
    @Async
    public void processRoomRobotMessages(LiveRoom room) {
        try {
            // 获取房间的机器人配置
            List<RoomRobotConfig> configs = getRoomRobotConfigs(room.getId());
            
            for (RoomRobotConfig config : configs) {
                if (config.getIsEnabled() == 1) {
                    processRobotConfig(room, config);
                }
            }
            
        } catch (Exception e) {
            log.error("处理房间机器人消息失败：roomId={}", room.getId(), e);
        }
    }

    /**
     * 处理单个机器人配置
     */
    private void processRobotConfig(LiveRoom room, RoomRobotConfig config) {
        try {
            String redisKey = String.format("robot:last_send:%d:%d", room.getId(), config.getRobotId());
            
            // 检查上次发送时间
            Long lastSendTime = (Long) redisTemplate.opsForValue().get(redisKey);
            long currentTime = System.currentTimeMillis();
            
            if (lastSendTime != null) {
                long timeDiff = (currentTime - lastSendTime) / 1000; // 转换为秒
                
                // 检查是否达到发送间隔
                if (timeDiff < config.getSendIntervalMin()) {
                    return; // 还没到发送时间
                }
                
                // 随机决定是否发送（在最小和最大间隔之间）
                if (timeDiff < config.getSendIntervalMax()) {
                    int probability = (int) ((timeDiff - config.getSendIntervalMin()) * 100 / 
                                           (config.getSendIntervalMax() - config.getSendIntervalMin()));
                    if (random.nextInt(100) > probability) {
                        return; // 随机决定不发送
                    }
                }
            }

            // 检查观众数量阈值
            if (room.getViewerCount() < config.getViewerThreshold()) {
                return; // 观众数量不足
            }

            // 检查每小时发送限制
            if (!checkHourlyLimit(room.getId(), config.getRobotId(), config.getMaxMessagesPerHour())) {
                return; // 超过每小时限制
            }

            // 选择触发条件
            String triggerCondition = selectTriggerCondition(room);
            
            // 发送机器人消息
            sendRobotMessage(room, config.getRobotId(), triggerCondition);
            
            // 更新最后发送时间
            redisTemplate.opsForValue().set(redisKey, currentTime, 1, TimeUnit.HOURS);
            
        } catch (Exception e) {
            log.error("处理机器人配置失败：roomId={}, robotId={}", room.getId(), config.getRobotId(), e);
        }
    }

    /**
     * 选择触发条件
     */
    private String selectTriggerCondition(LiveRoom room) {
        // 根据房间状态和观众数量选择触发条件
        if (room.getViewerCount() > room.getMaxViewerCount() * 0.8) {
            return "viewer_count_increase";
        } else {
            return "timer";
        }
    }

    /**
     * 发送机器人消息
     */
    private void sendRobotMessage(LiveRoom room, Long robotId, String triggerCondition) {
        try {
            // 获取机器人信息
            Robot robot = robotService.getById(robotId);
            if (robot == null || robot.getStatus() != 1) {
                return;
            }

            // 选择消息模板
            RobotMessage message = robotMessageService.selectRandomMessageByCondition(robotId, triggerCondition);
            if (message == null) {
                // 如果没有指定条件的消息，尝试获取任意消息
                List<RobotMessage> allMessages = robotMessageService.getMessagesByRobotId(robotId);
                message = robotMessageService.selectRandomMessage(allMessages);
            }

            if (message == null) {
                log.warn("机器人没有可用的消息模板：robotId={}", robotId);
                return;
            }

            // 保存评论到数据库
            Comment comment = commentService.sendRobotComment(room.getId(), robotId, message.getContent());

            // 构建WebSocket消息
            JSONObject commentData = new JSONObject();
            commentData.put("id", comment.getId());
            commentData.put("roomId", room.getId());
            commentData.put("robotId", robotId);
            commentData.put("content", message.getContent());
            commentData.put("isRobot", 1);
            commentData.put("createdTime", comment.getCreatedTime());
            
            // 添加机器人信息
            JSONObject robotInfo = new JSONObject();
            robotInfo.put("id", robot.getId());
            robotInfo.put("nickname", robot.getNickname());
            robotInfo.put("avatarUrl", robot.getAvatarUrl());
            robotInfo.put("gender", robot.getGender());
            commentData.put("robot", robotInfo);

            // 通过WebSocket发送到房间
            LiveWebSocketServer.sendCommentToRoom(room.getRoomCode(), commentData);

            // 更新机器人使用次数
            robotService.incrementUsageCount(robotId);

            // 记录发送日志
            recordRobotSendLog(room.getId(), robotId, message.getId(), message.getContent(), 
                             triggerCondition, room.getViewerCount());

            log.info("机器人发送消息成功：roomId={}, robotId={}, content={}", 
                    room.getId(), robotId, message.getContent());

        } catch (Exception e) {
            log.error("发送机器人消息失败：roomId={}, robotId={}", room.getId(), robotId, e);
        }
    }

    /**
     * 检查每小时发送限制
     */
    private boolean checkHourlyLimit(Long roomId, Long robotId, Integer maxMessagesPerHour) {
        String redisKey = String.format("robot:hourly_count:%d:%d:%d", 
                                       roomId, robotId, System.currentTimeMillis() / 3600000);
        
        Integer count = (Integer) redisTemplate.opsForValue().get(redisKey);
        if (count == null) {
            count = 0;
        }

        if (count >= maxMessagesPerHour) {
            return false;
        }

        // 增加计数
        redisTemplate.opsForValue().set(redisKey, count + 1, 1, TimeUnit.HOURS);
        return true;
    }

    /**
     * 获取房间的机器人配置
     */
    private List<RoomRobotConfig> getRoomRobotConfigs(Long roomId) {
        return roomRobotConfigService.getEnabledRoomConfigs(roomId);
    }

    /**
     * 记录机器人发送日志
     */
    private void recordRobotSendLog(Long roomId, Long robotId, Long messageId, String content, 
                                  String triggerType, Integer viewerCount) {
        // 这里可以实现发送日志记录功能
        // 保存到robot_send_logs表
        log.debug("记录机器人发送日志：roomId={}, robotId={}, triggerType={}, viewerCount={}", 
                 roomId, robotId, triggerType, viewerCount);
    }

    /**
     * 手动触发机器人发送消息
     */
    public boolean manualSendRobotMessage(Long roomId, Long robotId, String content) {
        try {
            LiveRoom room = liveRoomService.getById(roomId);
            if (room == null || room.getStatus() != 1) {
                log.warn("手动发送机器人消息失败：直播间不存在或未在直播中 - roomId={}", roomId);
                return false;
            }

            Robot robot = robotService.getById(robotId);
            if (robot == null || robot.getStatus() != 1) {
                log.warn("手动发送机器人消息失败：机器人不存在或已禁用 - robotId={}", robotId);
                return false;
            }

            // 保存评论到数据库
            Comment comment = commentService.sendRobotComment(roomId, robotId, content);

            // 构建WebSocket消息
            JSONObject commentData = new JSONObject();
            commentData.put("id", comment.getId());
            commentData.put("roomId", roomId);
            commentData.put("robotId", robotId);
            commentData.put("content", content);
            commentData.put("isRobot", 1);
            commentData.put("createdTime", comment.getCreatedTime());
            
            // 添加机器人信息
            JSONObject robotInfo = new JSONObject();
            robotInfo.put("id", robot.getId());
            robotInfo.put("nickname", robot.getNickname());
            robotInfo.put("avatarUrl", robot.getAvatarUrl());
            robotInfo.put("gender", robot.getGender());
            commentData.put("robot", robotInfo);

            // 通过WebSocket发送到房间
            LiveWebSocketServer.sendCommentToRoom(room.getRoomCode(), commentData);

            // 更新机器人使用次数
            robotService.incrementUsageCount(robotId);

            log.info("手动发送机器人消息成功：roomId={}, robotId={}, content={}", roomId, robotId, content);
            return true;

        } catch (Exception e) {
            log.error("手动发送机器人消息失败：roomId={}, robotId={}", roomId, robotId, e);
            return false;
        }
    }

    /**
     * 基于观众数量变化触发机器人消息
     */
    public void triggerByViewerCount(Long roomId, int oldCount, int newCount) {
        try {
            // 如果观众数量显著增加，触发机器人消息
            if (newCount > oldCount && newCount - oldCount >= 5) {
                LiveRoom room = liveRoomService.getById(roomId);
                if (room != null && room.getStatus() == 1) {
                    // 异步处理，避免阻塞主流程
                    processRoomRobotMessages(room);
                }
            }
        } catch (Exception e) {
            log.error("基于观众数量变化触发机器人消息失败：roomId={}", roomId, e);
        }
    }
}
